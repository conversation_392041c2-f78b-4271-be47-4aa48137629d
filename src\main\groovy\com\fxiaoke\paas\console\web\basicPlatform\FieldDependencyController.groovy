package com.fxiaoke.paas.console.web.basicPlatform

import com.fxiaoke.paas.console.annotation.SystemControllerLog
import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldDependencyResult
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.FieldDependencyService
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.servlet.ModelAndView

/**
 * 字段依赖控制器
 * 提供字段依赖分析的HTTP接口
 * <AUTHOR>
 * @date 2024/01/10
 */
@RestController
@RequestMapping("/basicPlatform/fieldDependency")
@Slf4j
class FieldDependencyController {

    @Autowired
    private FieldDependencyService fieldDependencyService

    /**
     * 分析字段依赖关系
     * @param arg 请求参数，包含objectApiName、fieldApiName
     * @param tenantId 企业ID
     * @return 字段依赖分析结果
     */
    @PostMapping(path = "/analyze", produces = "application/json;charset=utf-8")
    @SystemControllerLog(description = "基础平台 -- 字段依赖分析")
    @ResponseBody
    def analyze(@RequestBody Map arg, String tenantId) {
        // 参数验证
        def validationResult = validateParameters(arg, tenantId)
        if (validationResult != null) {
            return validationResult
        }

        String objectApiName = arg.get("objectApiName")
        String fieldApiName = arg.get("fieldApiName")

        try {
            log.info("开始分析字段依赖关系: tenantId=${tenantId}, objectApiName=${objectApiName}, fieldApiName=${fieldApiName}")
            
            // 调用服务层分析字段依赖关系
            FieldDependencyResult result = fieldDependencyService.analyzeDependency(tenantId, objectApiName, fieldApiName)
            
            log.info("字段依赖分析完成: 共发现 ${result.fieldList.size()} 个依赖字段")
            
            return ["code": 200, "data": result]
            
        } catch (IllegalArgumentException e) {
            log.warn("字段依赖分析参数错误: ${e.message}")
            return ["code": -100, "message": e.message]
            
        } catch (Exception e) {
            log.error("字段依赖分析失败: tenantId=${tenantId}, objectApiName=${objectApiName}, fieldApiName=${fieldApiName}", e)
            return ["code": -100, "message": "字段依赖分析失败: ${e.message}"]
        }
    }

    /**
     * 获取字段依赖关系的可视化数据
     * @param arg 请求参数，包含objectApiName、fieldApiName
     * @param tenantId 企业ID
     * @return 可视化数据
     */
    @PostMapping(path = "/visualize", produces = "application/json;charset=utf-8")
    @SystemControllerLog(description = "基础平台 -- 字段依赖分析可视化")
    @ResponseBody
    def visualize(@RequestBody Map arg, String tenantId) {
        // 参数验证
        def validationResult = validateParameters(arg, tenantId)
        if (validationResult != null) {
            return validationResult
        }

        String objectApiName = arg.get("objectApiName")
        String fieldApiName = arg.get("fieldApiName")

        try {
            log.info("开始生成字段依赖可视化数据: tenantId=${tenantId}, objectApiName=${objectApiName}, fieldApiName=${fieldApiName}")
            
            // 调用服务层分析字段依赖关系
            FieldDependencyResult result = fieldDependencyService.analyzeDependency(tenantId, objectApiName, fieldApiName)
            
            // 转换为可视化数据格式
            Map<String, Object> visualData = convertToVisualData(result)
            
            log.info("字段依赖可视化数据生成完成")
            
            return ["code": 200, "data": visualData]
            
        } catch (IllegalArgumentException e) {
            log.warn("字段依赖可视化参数错误: ${e.message}")
            return ["code": -100, "message": e.message]
            
        } catch (Exception e) {
            log.error("字段依赖可视化失败: tenantId=${tenantId}, objectApiName=${objectApiName}, fieldApiName=${fieldApiName}", e)
            return ["code": -100, "message": "字段依赖可视化失败: ${e.message}"]
        }
    }

    /**
     * 显示字段依赖页面
     * @return ModelAndView
     */
    @RequestMapping(value = "/page")
    ModelAndView page() {
        return new ModelAndView("basicPlatform/fieldDependency")
    }

    /**
     * 转换为可视化数据格式
     * @param result 字段依赖分析结果
     * @return 可视化数据
     */
    private static Map<String, Object> convertToVisualData(FieldDependencyResult result) {
        List<Map<String, Object>> nodes = []
        List<Map<String, Object>> edges = []

        // 构建节点数据
        result.fieldList.each { fieldId, fieldInfo ->
            nodes.add([
                id: fieldId,
                label: fieldInfo.field_label ?: fieldInfo.api_name,
                type: fieldInfo.type,
                objectApiName: fieldInfo.describe_api_name,
                isRoot: fieldId == result.rootFieldId
            ])
        }

        // 构建边数据
        result.dependencyGraph.each { fromFieldId, toFieldIds ->
            toFieldIds.each { toFieldId ->
                edges.add([
                    from: fromFieldId,
                    to: toFieldId
                ])
            }
        }

        return [
            nodes: nodes,
            edges: edges,
            rootFieldId: result.rootFieldId,
            objectApis: result.objectApis
        ]
    }

    /**
     * 参数验证公共方法
     * @param arg 请求参数
     * @param tenantId 企业ID
     * @return 验证失败时返回错误响应，成功时返回null
     */
    private static def validateParameters(Map arg, String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return ["code": -100, "message": "请输入企业ID"]
        }

        String objectApiName = arg.get("objectApiName")
        String fieldApiName = arg.get("fieldApiName")

        if (StringUtils.isAnyBlank(objectApiName, fieldApiName)) {
            return ["code": -100, "message": "请输入对象API名称和字段API名称"]
        }

        return null
    }
}

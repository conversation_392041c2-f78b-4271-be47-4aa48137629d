package com.fxiaoke.paas.console.service.basicPlatform.fielddependency.processor

import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldExt
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.FieldDescribeService
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.FieldProcessor
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * 主从字段处理器
 * 处理master_detail类型字段的依赖关系
 * <AUTHOR>
 * @date 2024/01/10
 */
@Component
@Slf4j
class MasterDetailFieldProcessor implements FieldProcessor {

    @Autowired
    private FieldDescribeService fieldDescribeService

    @Override
    List<FieldExt> processDependencies(FieldExt fieldExt, String tenantId) {
        // target_api_name 对应的主对象api
        String targetApiName = fieldExt.getTargetApiName()
        // 预先加载主对象的字段
        FieldExt idField = fieldDescribeService.loadObject(tenantId, targetApiName)
        // 主从字段不依赖别的字段
        return Collections.emptyList()
    }
}

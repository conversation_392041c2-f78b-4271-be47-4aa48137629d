package com.fxiaoke.paas.console.service.basicPlatform.fielddependency

import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldExt
import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldIdentifier
import com.fxiaoke.paas.console.mapper.metadata.FieldMapper
import com.google.common.collect.Maps
import groovy.util.logging.Slf4j
import jersey.repackaged.com.google.common.collect.Sets
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.web.context.annotation.RequestScope

/**
 * 基于FieldIdentifier的字段描述查询服务
 * 
 * 核心特性：
 * - 统一使用FieldIdentifier作为字段标识
 * - 分层缓存策略：字段级缓存 + 对象级缓存
 * - 智能预加载和懒加载机制
 * - 完善的错误处理和日志记录
 * 
 * <AUTHOR>
 * @date 2024/01/10
 */
@Service
@RequestScope
@Slf4j
class FieldDescribeService {

    @Autowired
    private FieldMapper fieldMapper

    // 字段级缓存：key = FieldIdentifier, value = FieldExt
    private final Map<FieldIdentifier, FieldExt> fieldCache = Maps.newConcurrentMap()
    // 已经加载的对象
    private final Set<String> objectCache = Sets.newConcurrentHashSet();

    /**
     * 预加载对象的全部字段到请求缓存
     * 
     * @param tenantId 企业ID
     * @param objectApiName 对象API名称
     */
    void loadObject(String tenantId, String objectApiName) {
        
        // 检查是否已缓存
        if (objectCache.contains(objectApiName)) {
            log.debug("对象字段已缓存，跳过预加载: ${objectApiName}")
            return
        }
        
        try {
            log.debug("开始预加载对象字段: ${objectApiName}")
            
            List<Map<String, Object>> fields = fieldMapper.setTenantId(tenantId)
                .findFieldByTenantIdAndDescribe(tenantId, objectApiName)
            
            for (Map<String, Object> fieldInfo : fields) {
                // 缓存字段信息
                FieldExt fieldExt = new FieldExt(fieldInfo)
                FieldIdentifier identifier = new FieldIdentifier(fieldExt)
                fieldCache.put(identifier, fieldExt)
            }
            
            // 缓存对象字段集合
            objectCache.add(objectApiName)
            
            log.info("预加载对象字段完成: ${objectApiName}, 共 ${fields.size()} 个字段")
            
        } catch (Exception e) {
            log.error("预加载对象字段失败: ${objectApiName}", e)
            throw new RuntimeException("预加载对象字段失败: ${objectApiName}", e)
        }
    }

    /**
     * 根据FieldIdentifier获取字段描述
     * 
     * @param tenantId 企业ID
     * @param identifier 字段标识符
     * @return 字段描述，如果不存在则返回null
     */
    private FieldExt getFieldByIdentifier(String tenantId, FieldIdentifier identifier) {
        if (identifier == null) {
            log.warn("FieldIdentifier为空，无法查询字段")
            return null
        }

        if(Objects.nonNull(identifier.getFieldExt())){
            return identifier.getFieldExt()
        }
        
        // 先从缓存获取
        FieldExt cached = fieldCache.get(identifier)
        if (cached != null) {
            log.debug("从缓存获取字段: ${identifier}")
            return cached
        }

        loadObject(tenantId, identifier.getObjectApiName())
        // 预加载后再次尝试从缓存获取
        cached = fieldCache.get(identifier)
        if (cached != null) {
            return cached
        }
        log.warn("未找到字段: ${identifier}")
        return null
    }

    /**
     * 按对象与字段API查询字段描述
     */
    FieldExt getFieldByObjectAndApi(String tenantId, String objectApiName, String fieldApiName) {
        FieldIdentifier identifier = new FieldIdentifier(objectApiName, fieldApiName)
        return getFieldByIdentifier(tenantId, identifier)
    }
}

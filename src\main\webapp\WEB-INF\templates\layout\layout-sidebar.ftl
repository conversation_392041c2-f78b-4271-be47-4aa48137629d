<div class="main-sidebar">
    <!-- Inner sidebar -->
    <div class="sidebar">
        <!-- Sidebar Menu -->
        <#--data-widget="tree"-->
        <ul class="sidebar-menu" data-widget="tree">
            <li class="header">导航栏</li>
            <#--元数据模块-->
            <li class="treeview">
                <a href="#">
                    <i class="fa fa-database"></i> <span>元数据</span>
                    <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
                </a>
                <ul class="treeview-menu">
                    <#--<li>-->
                    <#--<a href="${ctx}/metadata/stat/find"><i class="fa fa-bar-chart-o"></i>统计概况</a>-->
                    <#--</li>-->
                    <li>
                        <a href="${ctx}/metadata/describe/"><i class="fa fa-circle-o"></i>对象查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata/data/"><i class="fa fa-circle-o"></i>数据查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/table/special/created"><i class="fa fa-circle-o"></i>专表统计管理</a>
                    </li>
                    <#--<li>-->
                    <#--<a href="${ctx}/metadata/rpc/all"><i class="fa fa-circle-o"></i>全网统计</a>-->
                    <#--</li>-->
                    <li>
                        <a href="${ctx}/metadata/rule/find"><i class="fa fa-circle-o"></i>映射规则查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata/btn/find"><i class="fa fa-circle-o"></i>按钮动作查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata/rout/find"><i class="fa fa-circle-o"></i>路由查询</a>
                    </li>
                    <#--<li>-->
                    <#--<a href="${ctx}/metadata/option/find"><i class="fa fa-circle-o"></i>option查询</a>-->
                    <#--</li>-->
                    <li>
                        <a href="${ctx}/metadata/sql/query"><i class="fa fa-circle-o"></i>Sql查询与分析</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata/utils/"><i class="fa fa-circle-o"></i>元数据专表生成工具</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata/changeType"><i class="fa fa-circle-o"></i>changeType计算</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata/recycle/sql/page"><i class="fa fa-circle-o"></i>资源回收SQL查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata/option/query"><i class="fa fa-circle-o"></i>Option查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata/relation/query"><i class="fa fa-circle-o"></i>Relation查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata/pg/pg-info"><i class="fa fa-circle-o"></i>PG库信息查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/dmlmanager/page"><i class="fa fa-circle-o"></i>Schema管理</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata/sql/schema/page/schemaQuery"><i class="fa fa-circle-o"></i>Schema隔离企业SQL查询</a>
                    </li>

                    <li>
                        <a href="${ctx}/metadata/log-query/page"><i class="fa fa-circle-o"></i>刷库日志查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata/getSql/page"><i class="fa fa-circle-o"></i>Sql获取</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata/apiNameTransferPage"><i
                                    class="fa fa-circle-o"></i>ApiName<->StoreTableName</a>
                    </li>
                    <li>
                        <a href="http://log.foneshare.cn/app/kibana#/discover/AWK5jpoxmbWO3Cats6D_" target="_blank"><i
                                    class="fa fa-circle-o"></i>慢查询</a>
                    </li>

                    <li>
                        <a href="${ctx}/metadata/data/t2n_page" target="_blank"><i class="fa fa-circle-o"></i>刷Es编号字段</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata/purge"><i class="fa fa-circle-o"></i>清除缓存</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata/batch/purge"><i class="fa fa-circle-o"></i>批量清除缓存</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata-restore"><i class="fa fa-circle-o"></i>自定义对象恢复数据</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata/dataQuery/selectByScene"><i class="fa fa-circle-o"></i>模拟场景筛选</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata-repair/repairIndexName/"><i class="fa fa-circle-o"></i>修复index_name</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata-operate/add-capacity"><i class="fa fa-circle-o"></i>描述字段扩容</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata-operate/switch-unique"><i class="fa fa-circle-o"></i>变字段(可/不可)重复</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata-operate/field-recycle"><i class="fa fa-circle-o"></i>快速回收删除字段</a>
                    </li>
                    <li>
                        <a href="${ctx}/metadata-operate/fix-dirty-data"><i class="fa fa-circle-o"></i>修复脏数据</a>
                    </li>
                    <#--<li>-->
                    <#--<a href="${ctx}/stat/tree"><i class="fa fa-circle-o"></i>树形图</a>-->
                    <#--</li>-->
                </ul>
            </li>
            <#--dts-->
            <li class="treeview">
                <a href="#">
                    <i class="fa fa-info-circle"></i>
                    <span>DTS</span>
                    <span class="pull-right-container">
                        <i class="fa fa-angle-left pull-right"></i>
                    </span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="${ctx}/dts/get/config"><i class="fa fa-circle-o"></i>部署实施</a>
                    </li>
                    <li>
                        <a href="${ctx}/dts/check/data"><i class="fa fa-circle-o"></i>数据管理</a>
                    </li>
                </ul>
            </li>
            <#--流程模块-->
            <li class="treeview">
                <a href="#">
                    <i class="fa fa-info-circle"></i>
                    <span>流程</span>
                    <span class="pull-right-container">
                        <i class="fa fa-angle-left pull-right"></i>
                    </span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="${ctx}/workflow/source/flow/"><i class="fa fa-circle-o"></i>流程定义查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/workflow/instance/"><i class="fa fa-circle-o"></i>流程实例查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/workflow/task/"><i class="fa fa-circle-o"></i>Task查询</a>
                    </li>
                </ul>
            </li>
            <#--多语模块-->
            <li class="treeview">
                <a href="#">
                    <i class="fa fa-language"></i>
                    <span>多语言</span>
                    <span class="pull-right-container">
                        <i class="fa fa-angle-left pull-right"></i>
                    </span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="${ctx}/i18n/migrate/page"><i class="fa fa-language"></i>同步多语数据</a>
                    </li>
                </ul>
            </li>

            <#--数据权限模块-->
            <li class="treeview">
                <a href="#">
                    <i class="fa fa-sliders"></i>
                    <span>数据权限</span>
                    <span class="pull-right-container">
                        <i class="fa fa-angle-left pull-right"></i>
                    </span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="${ctx}/datarights/basic/query/"><i class="fa fa-circle-o"></i>对象权限查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/datarights/are/cache/query/"><i class="fa fa-circle-o"></i>共享规则缓存查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/datarights/shar/query/"><i class="fa fa-circle-o"></i>共享规则查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/datarights/leader/query/"><i class="fa fa-circle-o"></i>汇报对象缓存查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/datarights/authToUser"><i class="fa fa-circle-o"></i>人到人权限查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/datarights/dept/query/"><i class="fa fa-circle-o"></i>部门负责人缓存查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/datarights_refresh/permission/init/page"><i class="fa fa-circle-o"></i>缓存初始化</a>
                    </li>
                    <li>
                        <a href="${ctx}/datarights/map"><i class="fa fa-circle-o"></i>权限图谱</a>
                    </li>
                    <li>
                        <a href="${ctx}/datarights/query"><i class="fa fa-circle-o"></i>新数据权限查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/datarights/check"><i class="fa fa-circle-o"></i>权限检查与对象权限查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/datarights/mixBatch"><i class="fa fa-circle-o"></i>数据详情查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/datarights/user/circle/query"><i class="fa fa-circle-o"></i>我的圈子&权限重刷</a>
                    </li>
                    <li>
                        <a href="${ctx}/datarights/task/report"><i class="fa fa-circle-o"></i>权限计算任务</a>
                    </li>
                </ul>
            </li>


            <#--沙箱模块-->
            <li class="treeview">
                <a href="#">
                    <i class="fa fa-language"></i>
                    <span>沙箱拷贝</span>
                    <span class="pull-right-container">
                        <i class="fa fa-angle-left pull-right"></i>
                    </span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="${ctx}/sandBox/transaction/page"><i class="fa fa-language"></i>沙箱拷贝状态查询</a>
                    </li>
                </ul>
            </li>

            <#--功能权限模块-->
            <li class="treeview">
                <a href="#">
                    <i class="fa fa-cogs"></i>
                    <span>功能权限</span>
                    <span class="pull-right-container">
              <#--<span class="label label-primary pull-right">4</span>-->
                <i class="fa fa-angle-left pull-right"></i>
            </span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="${ctx}/func/role"><i class="fa fa-circle-o"></i>角色权限查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/func/old-api-role"><i class="fa fa-circle-o"></i>老角色接口查询角色</a>
                    </li>
                    <li>
                        <a href="${ctx}/func/user-role"><i class="fa fa-circle-o"></i>批量查询用户分配的角色</a>
                    </li>
                    <li>
                        <a href="${ctx}/func/find-user"><i class="fa fa-circle-o"></i>批量查询角色下的用户</a>
                    </li>
                    <li>
                        <a href="${ctx}/func/find-roles"><i class="fa fa-circle-o"></i>查询角色列表</a>
                    </li>
                    <li>
                        <a href="${ctx}/func/tenant-role-query"><i class="fa fa-circle-o"></i>企业角色查询(新)</a>
                    </li>
                    <li>
                        <a href="${ctx}/func/user-role-query"><i class="fa fa-circle-o"></i>角色下用户查询(新)</a>
                    </li>
                    <li>
                        <a href="${ctx}/paas-auth/system"><i class="fa fa-pencil-square-o"></i>预制系统库数据</a>
                    </li>
                </ul>
            </li>
            <#--全局搜索-->
            <li class="treeview">
                <a href="#">
                    <i class="fa fa-steam-square"></i>
                    <span>全局搜索</span>
                    <span class="pull-right-container">
                <i class="fa fa-angle-left pull-right"></i>
            </span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="${ctx}/hubble/hubble_query_refresh/page"><i class="fa fa-circle-o"></i>全局搜索查询和刷新</a>
                    </li>
                    <li>
                        <a href="${ctx}/hubble/query/block"><i class="fa fa-circle-o"></i>查询阻塞任务</a>
                    </li>
                </ul>
            </li>
            <#--分版模块-->
            <li class="treeview">
                <a href="#">
                    <i class="fa fa-life-ring"></i>
                    <span>分版查询</span>
                    <span class="pull-right-container">
              <#--<span class="label label-primary pull-right">4</span>-->
                <i class="fa fa-angle-left pull-right"></i>
            </span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="${ctx}/license/lice-version"><i class="fa fa-circle-o"></i>查询License版本</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/package"><i class="fa fa-circle-o"></i>查询企业资源包</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/module"><i class="fa fa-circle-o"></i>查询module</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/query/object"><i class="fa fa-circle-o"></i>查询下发对象</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/query/overview"><i class="fa fa-circle-o"></i> 查询概览</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/sql/query"><i class="fa fa-circle-o"></i>sql查询license</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/query-para"><i class="fa fa-circle-o"></i>查询配额</a>
                    </li>
                    <li>
                        <a href="${ctx}/license-write/update-para"><i class="fa fa-circle-o"></i>调整配额</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/query/expire"><i class="fa fa-circle-o"></i>查询指定产品到期时间</a>
                    </li>
                    <li>
                        <a href="${ctx}/license-write/send-mq"><i class="fa fa-circle-o"></i>发送创建MQ</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/query-count"><i class="fa fa-circle-o"></i>查询员工数</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/judge-module"><i class="fa fa-circle-o"></i>校验模块</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/checkAppValid"><i class="fa fa-circle-o"></i>校验指定产品是都有效</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/clear-catch"><i class="fa fa-circle-o"></i>清缓存</a>
                    </li>
                    <li>
                        <a href="${ctx}/license-write/gray-price"><i class="fa fa-circle-o"></i>添加灰度产品</a>
                    </li>
                    <li>
                        <a href="${ctx}/license-admin/add-license-object"><i class="fa fa-circle-o"></i>添加对象</a>
                    </li>
                    <li>
                        <a href="${ctx}/license-admin/handle-special-approval"><i class="fa fa-circle-o"></i>处理特殊审批</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/master-version"><i class="fa fa-circle-o"></i>主版本管理</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/all-version"><i class="fa fa-circle-o"></i>主版本详细数据</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/all-app"><i class="fa fa-circle-o"></i>应用详细数据</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/product-bought"><i class="fa fa-circle-o"></i>购买license企业</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/product-boughtV2"><i class="fa fa-circle-o"></i>购买license企业V2</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/query-object-template"><i class="fa fa-circle-o"></i>产品与对象绑定关系</a>
                    </li>
                    <li>
                        <a href="${ctx}/license/query-object-templateV2"><i class="fa fa-circle-o"></i>查询对象模版</a>
                    </li>
                </ul>
            </li>

            <#--组织架构模块-->
            <li class="treeview">
                <a href="#">
                    <i class="fa fa-object-ungroup"></i>
                    <span>组织架构</span>
                    <span class="pull-right-container">
              <#--<span class="label label-primary pull-right">4</span>-->
                <i class="fa fa-angle-left pull-right"></i>
            </span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="${ctx}/org/tenantMsg"><i class="fa fa-circle-o"></i>企业详细信息查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/org/treeDepartment"><i class="fa fa-circle-o"></i>部门树状图</a>
                    </li>
                    <li>
                        <a href="${ctx}/org/enterprise"><i class="fa fa-circle-o"></i>企业信息查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/org/department"><i class="fa fa-circle-o"></i>部门信息列表查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/org/subordinateDepartment"><i class="fa fa-circle-o"></i>下属部门信息查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/org/relationalDepartment"><i class="fa fa-circle-o"></i>部门信息关系查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/org/employee"><i class="fa fa-circle-o"></i>员工信息列表查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/org/relationalEmployee"><i class="fa fa-circle-o"></i>员工信息关系查询</a>
                    </li>
                    <li>
                        <a href="${ctx}/org/tel"><i class="fa fa-circle-o"></i>手机号查询员工信息</a>
                    </li>
                </ul>
            </li>

            <#--审计日志模块-->
            <li class="treeview">
                <a href="#">
                    <i class="fa fa-history"></i>
                    <span>审计日志</span>
                    <span class="pull-right-container">
                <i class="fa fa-angle-left pull-right"></i>
              <span id="auditLogTag" class="label label-primary pull-right"></span>
            </span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="${ctx}/audit/list"><i class="fa fa-circle-o"></i>操作记录</a>
                    </li>
                </ul>
            </li>

            <#--Schema迁移工具模块-->
            <li class="tree-view">
                <a href="#">
                    <i class="fa fa-cogs"></i>
                    <span>HAMSTER</span>
                    <span class="pull-right-container">
              <#--<span class="label label-primary pull-right">4</span>-->
                <i class="fa fa-angle-left pull-right"></i>
            </span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="${ctx}/hamster/task-list"><i class="fa fa-circle-o"></i>任务列表</a>
                    </li>
                    <li>
                        <a href="${ctx}/hamster/create-task"><i class="fa fa-circle-o"></i>新增任务</a>
                    </li>
                    <li>
                        <a href="${ctx}/hamster/supplement-migrate-task"><i class="fa fa-circle-o"></i>补迁数据</a>
                    </li>
                    <li>
                        <a href="${ctx}/data-validation/data-count"><i class="fa fa-circle-o"></i>数据验证</a>
                    </li>
                    <li>
                        <a href="${ctx}/data-validation/describe-data-validation"><i class="fa fa-circle-o"></i>新版数据验证</a>
                    </li>
                    <li>
                        <a href="${ctx}/hamster/console-log"><i class="fa fa-circle-o"></i>审计日志</a>
                    </li>
                </ul>
            </li>

            <li class="base-platform">
                <a href="#">
                    <i class="fa fa-steam-square"></i>
                    <span>基础平台</span>
                    <span class="pull-right-container">
              <#--<span class="label label-primary pull-right">4</span>-->
                <i class="fa fa-angle-left pull-right"></i>
            </span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="${ctx}/basicPlatform/calculate/page"><i class="fa fa-circle-o"></i>计算工具</a>
                    </li>
                    <li>
                        <a href="${ctx}/basicPlatform/duplicated/page"><i class="fa fa-circle-o"></i>查重工具</a>
                    </li>
                    <li>
                        <a href="${ctx}/basicPlatform/schedulerTask/page"><i class="fa fa-circle-o"></i>计划任务</a>
                    </li>
                    <li>
                        <a href="${ctx}/basicPlatform/fieldDependency/page"><i class="fa fa-circle-o"></i>字段依赖分析</a>
                    </li>
                </ul>
            </li>

        </ul><!-- /.sidebar-menu -->
    </div><!-- /.sidebar -->
</div><!-- /.main-sidebar -->

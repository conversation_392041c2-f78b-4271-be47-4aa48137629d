package com.fxiaoke.paas.console.service.basicPlatform.fielddependency.processor

import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldExt
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.FieldProcessor
import groovy.util.logging.Slf4j
import org.springframework.stereotype.Component

/**
 * 默认空处理器
 * 作为兜底处理器，不处理任何字段类型
 * <AUTHOR>
 * @date 2024/01/10
 */
@Component
@Slf4j
class EmptyFieldProcessor implements FieldProcessor {

    @Override
    List<FieldExt> processDependencies(FieldExt fieldExt, String tenantId) {
        // 返回空列表，表示无依赖
        log.debug("EmptyFieldProcessor处理字段: ${fieldExt.apiName}, 无依赖关系")
        return Collections.emptyList()
    }
}

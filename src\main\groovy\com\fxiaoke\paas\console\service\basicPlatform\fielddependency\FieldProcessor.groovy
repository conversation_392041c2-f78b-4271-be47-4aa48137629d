package com.fxiaoke.paas.console.service.basicPlatform.fielddependency

import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldExt

/**
 * 字段处理器接口
 * 用于解析当前字段描述依赖的别的字段描述
 * <AUTHOR>
 * @date 2024/01/10
 */
interface FieldProcessor {
    
    /**
     * 处理字段依赖关系
     * @param fieldExt 字段信息包装对象
     * @param tenantId 企业ID
     * @return 依赖的字段描述列表
     */
    List<FieldExt> processDependencies(FieldExt fieldExt, String tenantId)
}

package com.fxiaoke.paas.console.service.basicPlatform.fielddependency.processor

import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldExt
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.FieldProcessor
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.util.ExpressionParser
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * 计算字段处理器
 * 处理formula类型字段的依赖关系
 * <AUTHOR>
 * @date 2024/01/10
 */
@Component
@Slf4j
class FormulaFieldProcessor implements FieldProcessor {

    @Autowired
    private ExpressionParser expressionParser

    @Override
    List<FieldExt> processDependencies(FieldExt fieldExt, String tenantId) {
        List<FieldExt> dependencies = []
        
        try {
            // 解析逻辑：
            // 需要解析 expression 对应的表达式
            // 示例: "$field_j1__c$ + $field_j2__c__r.field_3__c$"
            String expression = fieldExt.getExpression()
            String currentObjectApiName = fieldExt.getDescribeApiName()
            
            if (StringUtils.isNotBlank(expression) && StringUtils.isNotBlank(currentObjectApiName)) {
                // 使用ExpressionParser解析表达式
                List<FieldExt> expressionFields = expressionParser.parseExpression(expression, currentObjectApiName, tenantId)
                dependencies.addAll(expressionFields)
                
                log.debug("Formula字段 ${fieldExt.apiName} 依赖字段")
            }
        } catch (Exception e) {
            log.warn("处理Formula字段依赖失败: ${fieldExt.apiName}", e)
        }

        return dependencies
    }
}

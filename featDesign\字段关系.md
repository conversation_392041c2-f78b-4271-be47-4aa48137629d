# 新工具: 字段依赖关系

## 需求分析和设计

### 功能边界
- **入参**: 企业id, 对象apiName, 字段apiName
- **结果**: 递归查询其依赖的全部字段描述, 字段描述之间的关系(使用邻接表表示的有多条出边的有向图), 涉及的对象api列表(附上跳转链接)

### 数据模型
- 一个对象下有若干字段
- 利用现有的FieldMapper和DescribeService查询逻辑
- 字段信息通过`FieldMapper.findFieldByTenantIdAndDescribe()`获取
- 对象信息通过`DescribeService.loadDescribeFromPg()`获取

### 技术架构设计

#### 包结构 (继续使用现有模式)
```
src/main/groovy/com/fxiaoke/paas/console/
├── bean/basicPlatform/fielddependency/     # DTO对象
│   ├── FieldDependencyResult.groovy        # 字段依赖分析结果
│   ├── FieldNode.groovy                    # 字段节点信息
│   ├── FieldEdge.groovy                    # 字段依赖关系边
│   └── ObjectInfo.groovy                   # 对象信息
├── service/basicPlatform/fielddependency/  # 业务逻辑服务
│   ├── FieldDependencyService.groovy       # 主服务类
│   ├── processor/                          # 字段类型处理器
│   │   ├── FieldProcessor.groovy           # 处理器接口
│   │   ├── MasterDetailFieldProcessor.groovy
│   │   ├── LookupFieldProcessor.groovy
│   │   ├── FormulaFieldProcessor.groovy
│   │   ├── CountFieldProcessor.groovy
│   │   ├── QuoteFieldProcessor.groovy
│   │   └── DefaultValueFieldProcessor.groovy
│   └── util/
│       └── ExpressionParser.groovy         # 表达式解析工具
└── web/basicPlatform/
    └── FieldDependencyController.groovy    # 控制器
```

### 创建实体类/数据访问层
复用现有逻辑：
- **FieldMapper**: 字段查询
- **DescribeService**: 对象查询
- **数据源**: mt_field表和mt_describe表

## 核心DTO设计

### FieldDependencyResult - 字段依赖分析结果
```groovy
@Data
class FieldDependencyResult {
    Map<String, Map<String, Object>> fieldList    // 字段描述，key为field_id，value为字段完整信息
    Map<String, List<String>> dependencyGraph     // 使用邻接表表示的有向图，key为field_id，value为依赖的field_id列表
    List<String> objectApis                       // 涉及的对象API列表
    String rootFieldId                            // 根字段ID(field_id)
}
```

**设计说明：**
- **fieldList**: 使用field_id作为key，存储字段的完整信息，数据和结构分离
- **dependencyGraph**: 邻接表表示的有向图，key是源字段field_id，value是该字段依赖的所有字段field_id列表
- **objectApis**: 只返回对象API名称列表，减少回参体积，前端可根据需要构造跳转链接
- **rootFieldId**: 使用数据库中的field_id作为根字段标识

## 公共包装类设计

### FieldExt - 字段信息包装类
```groovy
@Data
class FieldExt {
    private Map<String, Object> fieldInfo

    FieldExt(Map<String, Object> fieldInfo) {
        this.fieldInfo = fieldInfo ?: [:]
    }

    // 基础字段信息
    String getFieldId() { return fieldInfo.get("field_id") as String }
    String getApiName() { return fieldInfo.get("api_name") as String }
    String getFieldLabel() { return fieldInfo.get("field_label") as String ?: "" }
    String getDescribeApiName() { return fieldInfo.get("describe_api_name") as String }
    String getType() { return fieldInfo.get("type") as String ?: "text" }

    // 关联字段信息
    String getTargetApiName() { return fieldInfo.get("target_api_name") as String }
    String getWheres() { return fieldInfo.get("wheres") as String }

    // 计算字段信息
    String getExpression() { return fieldInfo.get("expression") as String }

    // 统计字段信息
    String getSubObjectDescribeApiName() { return fieldInfo.get("sub_object_describe_api_name") as String }
    String getCountFieldApiName() { return fieldInfo.get("count_field_api_name") as String }

    // 引用字段信息
    String getQuoteField() { return fieldInfo.get("quote_field") as String }

    // 默认值信息
    String getDefaultValue() { return fieldInfo.get("default_value") as String }
    Boolean getDefaultIsExpression() {
        def value = fieldInfo.get("default_is_expression")
        return value != null ? (value as Boolean) : false
    }

    // 获取原始字段信息
    Map<String, Object> getRawFieldInfo() { return fieldInfo }
}
```

### ObjectExt - 对象信息包装类
```groovy
@Data
class ObjectExt {
    private Map<String, Object> objectInfo

    ObjectExt(Map<String, Object> objectInfo) {
        this.objectInfo = objectInfo ?: [:]
    }

    String getDescribeId() { return objectInfo.get("describe_id") }
    String getDescribeApiName() { return objectInfo.get("describe_api_name") }
    String getDisplayName() { return objectInfo.get("display_name") }
    String getStoreTableName() { return objectInfo.get("store_table_name") }

    // 获取原始对象信息
    Map<String, Object> getRawObjectInfo() { return objectInfo }
}
```

## 数据访问层设计

### FieldMapper接口说明
使用现有的FieldMapper API，不做修改：
```groovy
// 使用现有的FieldMapper API
fieldMapper.setTenantId(tenantId).findFieldByTenantIdAndDescribe(tenantId, objectApiName)
fieldMapper.setTenantId(tenantId).findFieldByFieldId(fieldId)
```

## 业务逻辑层设计

### 核心算法设计
- **BFS遍历**: 以字段为单位进行层序遍历，利用队列实现，避免递归
- **最大层级限制**: 静态变量MAX_LEVEL = 3，防止无限循环
- **去重机制**: 使用Set记录已访问的字段ID，避免重复处理
- **循环依赖检测**: 在BFS过程中检测循环依赖，及时终止
- **策略模式**: 为每种字段类型创建专门的处理器
- **缓存优化**: 使用Spring Cache缓存字段查询结果

### FieldDependencyService - 主服务类
```groovy
@Service
class FieldDependencyService {

    @Value("${field.dependency.max.level:3}")
    private Integer maxLevel

    @Autowired
    private FieldMapper fieldMapper
    @Autowired
    private DescribeService describeService
    @Autowired
    private FieldProcessorRouter processorRouter
    @Autowired
    private CacheManager cacheManager

    // 字段信息本地缓存，key为field_id，value为FieldExt
    private Map<String, FieldExt> localFieldCache = Maps.newConcurrentMap()
    // 对象字段映射缓存，key为objectApiName，value为该对象下所有字段的field_id列表
    private Map<String, Set<String>> objectFieldMapping = Maps.newConcurrentMap()

    /**
     * 分析字段依赖关系
     * @param tenantId 企业ID
     * @param objectApiName 对象API名称
     * @param fieldApiName 字段API名称
     * @return 字段依赖分析结果
     */
    FieldDependencyResult analyzeDependency(String tenantId, String objectApiName, String fieldApiName) {
        // 1. 清空本次分析的本地缓存
        localFieldCache.clear()
        objectFieldMapping.clear()

        // 2. 预加载根对象的所有字段
        loadObjectFields(tenantId, objectApiName)

        // 3. 查询根字段信息
        FieldExt rootField = getFieldFromCache(tenantId, objectApiName, fieldApiName)
        if (rootField == null) {
            throw new IllegalArgumentException("字段不存在: ${objectApiName}.${fieldApiName}")
        }

        // 4. 初始化结果对象
        FieldDependencyResult result = new FieldDependencyResult()
        result.rootFieldId = rootField.getFieldId()
        result.fieldList = [:]
        result.dependencyGraph = [:]
        result.objectApis = []

        // 5. BFS遍历依赖关系(按字段维度)
        traverseDependencies(rootField, result, tenantId)

        // 6. 收集涉及的对象API
        Set<String> objectApiSet = result.fieldList.values().collect {
            it.describe_api_name
        }.toSet()
        result.objectApis = objectApiSet.toList()

        return result
    }

    /**
     * BFS遍历依赖关系(按字段维度)
     * @param rootField 根字段
     * @param result 结果对象
     * @param tenantId 企业ID
     */
    private void traverseDependencies(FieldExt rootField, FieldDependencyResult result, String tenantId) {
        Queue<String> fieldQueue = new LinkedList<>()
        Set<String> visitedFields = new HashSet<>()
        Map<String, Integer> fieldLevels = [:]

        // 初始化根字段
        String rootFieldId = rootField.getFieldId()
        fieldQueue.offer(rootFieldId)
        visitedFields.add(rootFieldId)
        fieldLevels.put(rootFieldId, 0)

        // 添加根字段到结果
        result.fieldList.put(rootFieldId, rootField.getRawFieldInfo())
        result.dependencyGraph.put(rootFieldId, [])

        while (!fieldQueue.isEmpty()) {
            String currentFieldId = fieldQueue.poll()
            Integer currentLevel = fieldLevels.get(currentFieldId)

            // 检查层级限制
            if (currentLevel >= maxLevel) {
                continue
            }

            // 获取当前字段信息
            FieldExt currentField = getFieldFromCacheById(tenantId, currentFieldId)
            if (currentField == null) {
                continue
            }

            // 分析当前字段的依赖
            List<String> dependencies = processorRouter.getProcessor(currentField)
                .processDependencies(currentField, tenantId)

            // 检测循环依赖
            if (dependencies.contains(currentFieldId)) {
                log.warn("检测到循环依赖: ${currentFieldId}")
                continue
            }

            // 更新依赖图
            result.dependencyGraph.put(currentFieldId, dependencies)

            // 处理依赖字段
            for (String depFieldId : dependencies) {
                if (!visitedFields.contains(depFieldId)) {
                    // 获取依赖字段信息
                    FieldExt depField = getFieldFromCacheById(tenantId, depFieldId)
                    if (depField != null) {
                        fieldQueue.offer(depFieldId)
                        visitedFields.add(depFieldId)
                        fieldLevels.put(depFieldId, currentLevel + 1)

                        // 添加到结果
                        result.fieldList.put(depFieldId, depField.getRawFieldInfo())
                    }
                }
            }
        }
    }

    /**
     * 加载对象的所有字段到本地缓存
     * @param tenantId 企业ID
     * @param objectApiName 对象API名称
     */
    private void loadObjectFields(String tenantId, String objectApiName) {
        try {
            def fields = fieldMapper.setTenantId(tenantId)
                .findFieldByTenantIdAndDescribe(tenantId, objectApiName)

            Set<String> fieldIds = []
            fields.each { fieldInfo ->
                String fieldId = fieldInfo.field_id
                if (fieldId) {
                    FieldExt fieldExt = new FieldExt(fieldInfo)
                    localFieldCache.put(fieldId, fieldExt)
                    fieldIds.add(fieldId)
                }
            }

            objectFieldMapping.put(objectApiName, fieldIds)
            log.debug("加载对象字段完成: ${objectApiName}, 字段数量: ${fieldIds.size()}")
        } catch (Exception e) {
            log.error("加载对象字段失败: ${objectApiName}", e)
        }
    }

    /**
     * 从本地缓存获取字段信息，如果不存在则查询数据库
     * @param tenantId 企业ID
     * @param objectApiName 对象API名称
     * @param fieldApiName 字段API名称
     * @return 字段信息包装对象
     */
    private FieldExt getFieldFromCache(String tenantId, String objectApiName, String fieldApiName) {
        // 1. 先检查是否已加载该对象的字段
        if (!objectFieldMapping.containsKey(objectApiName)) {
            loadObjectFields(tenantId, objectApiName)
        }

        // 2. 从本地缓存中查找
        Set<String> fieldIds = objectFieldMapping.get(objectApiName)
        if (fieldIds) {
            for (String fieldId : fieldIds) {
                FieldExt fieldExt = localFieldCache.get(fieldId)
                if (fieldExt && fieldApiName.equals(fieldExt.getApiName())) {
                    return fieldExt
                }
            }
        }

        return null
    }

    /**
     * 根据字段ID从本地缓存获取字段信息，如果不存在则查询数据库
     * @param tenantId 企业ID
     * @param fieldId 字段ID
     * @return 字段信息包装对象
     */
    private FieldExt getFieldFromCacheById(String tenantId, String fieldId) {
        // 1. 先从本地缓存查找
        FieldExt fieldExt = localFieldCache.get(fieldId)
        if (fieldExt != null) {
            return fieldExt
        }

        // 2. 本地缓存没有，查询数据库
        try {
            def fieldInfo = fieldMapper.setTenantId(tenantId).findFieldByFieldId(fieldId)
            if (fieldInfo) {
                fieldExt = new FieldExt(fieldInfo)
                localFieldCache.put(fieldId, fieldExt)

                // 同时加载该字段所属对象的所有字段
                String objectApiName = fieldExt.getDescribeApiName()
                if (objectApiName && !objectFieldMapping.containsKey(objectApiName)) {
                    loadObjectFields(tenantId, objectApiName)
                }

                return fieldExt
            }
        } catch (Exception e) {
            log.error("查询字段失败: ${fieldId}", e)
        }

        return null
    }
}
```

### 字段类型处理器设计
使用策略模式，每种字段类型对应一个处理器：

#### FieldProcessorRouter - 处理器路由类
```groovy
@Component
class FieldProcessorRouter {

    @Autowired
    private List<FieldProcessor> processors

    /**
     * 根据字段信息获取对应的处理器
     * @param fieldExt 字段信息包装对象
     * @return 对应的处理器
     */
    FieldProcessor getProcessor(FieldExt fieldExt) {
        // 按优先级查找处理器
        for (FieldProcessor processor : processors) {
            if (processor.canProcess(fieldExt)) {
                return processor
            }
        }

        // 默认返回空处理器
        return new EmptyFieldProcessor()
    }
}
```

#### FieldProcessor - 处理器接口
```groovy
interface FieldProcessor {
    /**
     * 判断是否可以处理该字段
     * @param fieldExt 字段信息包装对象
     * @return 是否可以处理
     */
    boolean canProcess(FieldExt fieldExt)

    /**
     * 处理字段依赖关系
     * @param fieldExt 字段信息包装对象
     * @param tenantId 企业ID
     * @return 依赖的字段field_id列表
     */
    List<String> processDependencies(FieldExt fieldExt, String tenantId)
}
```


## 字段类型解析逻辑详解

### 1. MasterDetailFieldProcessor - 主从字段处理器
```groovy
@Component
class MasterDetailFieldProcessor implements FieldProcessor {
    boolean canProcess(FieldExt fieldExt) {
        return "master_detail".equals(fieldExt.getType())
    }

    List<String> processDependencies(FieldExt fieldExt, String tenantId) {
        // 解析逻辑：
        // target_api_name 对应的主对象api
        String targetApiName = fieldExt.getTargetApiName()
        // 查询目标对象的主键字段，返回其field_id
        // 主从关系通常依赖于主对象的主键字段
    }
}
```

### 2. LookupFieldProcessor - Lookup字段处理器
```groovy
@Component
class LookupFieldProcessor implements FieldProcessor {
    boolean canProcess(FieldExt fieldExt) {
        String fieldType = fieldExt.getType()
        return "object_reference".equals(fieldType) || "object_reference_many".equals(fieldType)
    }

    List<String> processDependencies(FieldExt fieldExt, String tenantId) {
        // 解析逻辑：
        // 1. target_api_name 对应的目标对象api
        // 2. 解析 wheres 字段，获取涉及的目标对象字段
        // 示例: "[{\"filters\": [{\"operator\": \"IS\", \"field_name\": \"field_xLe1P__c\", \"value_type\": 0, \"field_values\": []}], \"connector\": \"OR\"}]"
        // field_name对应的field_xLe1P__c为目标对象的字段
        // 返回目标对象中被引用字段的field_id列表
        String targetApiName = fieldExt.getTargetApiName()
        String wheres = fieldExt.getWheres()
        // 使用ExpressionParser解析wheres条件
    }
}
```

### 3. FormulaFieldProcessor - 计算字段处理器
```groovy
@Component
class FormulaFieldProcessor implements FieldProcessor {
    boolean canProcess(FieldExt fieldExt) {
        return "formula".equals(fieldExt.getType())
    }

    List<String> processDependencies(FieldExt fieldExt, String tenantId) {
        // 解析逻辑：
        // 需要解析 expression 对应的表达式
        // 示例: "$field_j1__c$ + $field_j2__c__r.field_3__c$"
        // 使用$$包裹的是变量，当变量为 xxx__c 或者 xxx__c__r.xxx__c 时需要解析
        // xxx__c 为本对象其他字段，返回其field_id
        // xxx__c__r.xxx__c 中 xxx__c__r 去掉 __r 的字符串是本对象lookup字段/主从字段
        // "."后的为lookup字段/主从字段目标对象的字段，返回目标字段的field_id
        // 返回所有依赖字段的field_id列表
        String expression = fieldExt.getExpression()
        String currentObjectApiName = fieldExt.getDescribeApiName()
        // 使用ExpressionParser解析表达式
    }
}
```

### 4. CountFieldProcessor - 统计字段处理器
```groovy
@Component
class CountFieldProcessor implements FieldProcessor {
    boolean canProcess(FieldExt fieldExt) {
        return "count".equals(fieldExt.getType())
    }

    List<String> processDependencies(FieldExt fieldExt, String tenantId) {
        // 解析逻辑：
        // 1. sub_object_describe_api_name为被统计的目标对象
        // 2. wheres和lookup字段一致，解析过滤条件中的字段
        // 3. field_api_name 被统计对象上，关联到本对象的lookup字段/主从字段
        // 4. count_field_api_name 被统计对象上的被统计的目标字段
        // 返回相关字段的field_id列表
        String subObjectApiName = fieldExt.getSubObjectDescribeApiName()
        String wheres = fieldExt.getWheres()
        String countFieldApiName = fieldExt.getCountFieldApiName()
        // 使用ExpressionParser解析wheres条件
    }
}
```

### 5. QuoteFieldProcessor - 引用字段处理器
```groovy
@Component
class QuoteFieldProcessor implements FieldProcessor {
    boolean canProcess(FieldExt fieldExt) {
        return "quote".equals(fieldExt.getType())
    }

    List<String> processDependencies(FieldExt fieldExt, String tenantId) {
        // 解析逻辑：
        // 解析 quote_field, 值类似 "field_mk48c__c__r.field_xLe1P__c"
        // field_mk48c__c 表示本对象lookup字段/主从字段，返回其field_id
        // field_xLe1P__c 为lookup字段/主从字段目标对象的字段，返回其field_id
        // 返回相关字段的field_id列表
        String quoteField = fieldExt.getQuoteField()
        String currentObjectApiName = fieldExt.getDescribeApiName()
        // 使用ExpressionParser解析quote_field
    }
}
```

### 6. DefaultValueFieldProcessor - 默认值字段处理器
```groovy
@Component
class DefaultValueFieldProcessor implements FieldProcessor {
    boolean canProcess(FieldExt fieldExt) {
        // 只处理default_is_expression为true的字段
        Boolean defaultIsExpression = fieldExt.getDefaultIsExpression()
        return defaultIsExpression != null && defaultIsExpression
    }

    List<String> processDependencies(FieldExt fieldExt, String tenantId) {
        // 解析逻辑：
        // 需要解析default_value字段，规则和expression一致
        // 返回依赖字段的field_id列表
        String defaultValue = fieldExt.getDefaultValue()
        String currentObjectApiName = fieldExt.getDescribeApiName()
        // 使用ExpressionParser解析default_value表达式
    }
}
```

### 7. EmptyFieldProcessor - 空处理器
```groovy
@Component
class EmptyFieldProcessor implements FieldProcessor {
    boolean canProcess(FieldExt fieldExt) {
        // 兜底处理器，不处理任何字段
        return false
    }

    List<String> processDependencies(FieldExt fieldExt, String tenantId) {
        // 返回空列表，表示无依赖
        return Collections.emptyList()
    }
}
```


## 表达式解析工具设计

### ExpressionParser - 表达式解析器
```groovy
@Component
class ExpressionParser {

    @Autowired
    private FieldMapper fieldMapper

    /**
     * 解析表达式中的字段引用
     * @param expression 表达式字符串，如 "$field_j1__c$ + $field_j2__c__r.field_3__c$"
     * @param currentObjectApiName 当前对象API名称
     * @param tenantId 企业ID
     * @return 依赖的字段field_id列表
     */
    List<String> parseExpression(String expression, String currentObjectApiName, String tenantId) {
        if (StringUtils.isBlank(expression)) {
            return Collections.emptyList()
        }

        List<String> fieldIds = []
        // 1. 使用正则表达式提取$$包裹的变量: \$([^$]+)\$
        Pattern pattern = Pattern.compile(/\$([^$]+)\$/)
        Matcher matcher = pattern.matcher(expression)

        while (matcher.find()) {
            String variable = matcher.group(1)
            fieldIds.addAll(parseVariable(variable, currentObjectApiName, tenantId))
        }

        return fieldIds.unique()
    }

    /**
     * 解析wheres条件中的字段引用
     * @param wheres JSON字符串
     * @param targetObjectApiName 目标对象API名称
     * @param tenantId 企业ID
     * @return 依赖的字段field_id列表
     */
    List<String> parseWheres(String wheres, String targetObjectApiName, String tenantId) {
        if (StringUtils.isBlank(wheres)) {
            return Collections.emptyList()
        }

        try {
            List<String> fieldIds = []
            def wheresList = JsonSlurper().parseText(wheres)

            wheresList.each { whereClause ->
                def filters = whereClause.filters
                filters?.each { filter ->
                    String fieldName = filter.field_name
                    if (StringUtils.isNotBlank(fieldName)) {
                        String fieldId = getFieldIdByApiName(tenantId, targetObjectApiName, fieldName)
                        if (fieldId) {
                            fieldIds.add(fieldId)
                        }
                    }
                }
            }

            return fieldIds.unique()
        } catch (Exception e) {
            log.warn("解析wheres条件失败: ${wheres}", e)
            return Collections.emptyList()
        }
    }

    /**
     * 解析quote_field中的字段引用
     * @param quoteField 如 "field_mk48c__c__r.field_xLe1P__c"
     * @param currentObjectApiName 当前对象API名称
     * @param tenantId 企业ID
     * @return 依赖的字段field_id列表
     */
    List<String> parseQuoteField(String quoteField, String currentObjectApiName, String tenantId) {
        if (StringUtils.isBlank(quoteField)) {
            return Collections.emptyList()
        }

        return parseVariable(quoteField, currentObjectApiName, tenantId)
    }

    /**
     * 解析变量引用
     * @param variable 变量名，如 "field_j1__c" 或 "field_j2__c__r.field_3__c"
     * @param currentObjectApiName 当前对象API名称
     * @param tenantId 企业ID
     * @return 依赖的字段field_id列表
     */
    private List<String> parseVariable(String variable, String currentObjectApiName, String tenantId) {
        List<String> fieldIds = []

        if (variable.contains('.')) {
            // 关联字段引用: xxx__c__r.yyy__c
            String[] parts = variable.split('\\.')
            if (parts.length == 2) {
                String lookupFieldName = parts[0].replace('__r', '')  // 去掉__r后缀
                String targetFieldName = parts[1]

                // 1. 查询本对象的lookup字段
                String lookupFieldId = getFieldIdByApiName(tenantId, currentObjectApiName, lookupFieldName)
                if (lookupFieldId) {
                    fieldIds.add(lookupFieldId)

                    // 2. 查询目标对象的字段
                    // 需要先获取lookup字段的target_api_name
                    String targetObjectApiName = getLookupTargetObjectApiName(tenantId, lookupFieldId)
                    if (targetObjectApiName) {
                        String targetFieldId = getFieldIdByApiName(tenantId, targetObjectApiName, targetFieldName)
                        if (targetFieldId) {
                            fieldIds.add(targetFieldId)
                        }
                    }
                }
            }
        } else {
            // 本对象字段引用: xxx__c
            String fieldId = getFieldIdByApiName(tenantId, currentObjectApiName, variable)
            if (fieldId) {
                fieldIds.add(fieldId)
            }
        }

        return fieldIds
    }
}

    /**
     * 辅助方法：根据对象API名称和字段API名称查询field_id
     * @param tenantId 企业ID
     * @param objectApiName 对象API名称
     * @param fieldApiName 字段API名称
     * @return field_id
     */
    private String getFieldIdByApiName(String tenantId, String objectApiName, String fieldApiName) {
        // 先从本地缓存查找
        FieldExt fieldExt = getFieldFromCache(tenantId, objectApiName, fieldApiName)
        return fieldExt?.getFieldId()
    }

    /**
     * 获取lookup字段的目标对象API名称
     * @param tenantId 企业ID
     * @param fieldId 字段ID
     * @return 目标对象API名称
     */
    private String getLookupTargetObjectApiName(String tenantId, String fieldId) {
        // 先从本地缓存查找
        FieldExt fieldExt = getFieldFromCacheById(tenantId, fieldId)
        return fieldExt?.getTargetApiName()
    }
}
```

## 控制器层及前端页面设计

### FieldDependencyController - 控制器
```groovy
@Controller
@RequestMapping("/basicPlatform/fieldDependency")
class FieldDependencyController {

    @Autowired
    private FieldDependencyService fieldDependencyService

    /**
     * 字段依赖分析页面
     */
    @RequestMapping("/page")
    def page() {
        return "basicPlatform/fieldDependency"
    }

    /**
     * 分析字段依赖关系
     */
    @RequestMapping("/analyze")
    @ResponseBody
    @SystemControllerLog(description = "基础平台 -- 字段依赖关系分析")
    def analyze(@RequestParam String tenantId,
                @RequestParam String objectApiName,
                @RequestParam String fieldApiName) {
        try {
            FieldDependencyResult result = fieldDependencyService.analyzeDependency(tenantId, objectApiName, fieldApiName)
            return ["code": 200, "data": result]
        } catch (Exception e) {
            log.error("字段依赖分析失败", e)
            return ["code": 500, "message": e.getMessage()]
        }
    }
}
```

### 前端页面设计
在计算工具下新增"字段依赖"tab：

#### 修改 basicPlatform/calculateTool.ftl
```html
<!-- 在现有tab列表中添加 -->
<li>
    <a href="#fieldDependency" data-toggle="tab">字段依赖</a>
</li>

<!-- 在tab-content中添加 -->
<div class="fieldDependency tab-pane" id="fieldDependency">
    <div class="form-inline formBox">
        <input type="text" id="inputTenantIdDep" placeholder="请输入企业ei或ea">
        <input type="text" id="inputObjectApiNameDep" placeholder="请输入对象ApiName">
        <input type="text" id="inputFieldApiNameDep" placeholder="请输入字段ApiName">
        <button type="button" id="analyzeDependency" class="btn btn-primary">分析依赖关系</button>
    </div>

    <!-- 结果展示区域 -->
    <div class="row">
        <div class="col-md-6">
            <h4>字段信息</h4>
            <div id="fieldList"></div>
        </div>
        <div class="col-md-6">
            <h4>依赖关系图</h4>
            <div id="dependencyGraph"></div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <h4>涉及对象</h4>
            <div id="objectApis"></div>
        </div>
    </div>
</div>
```

#### 侧边栏菜单更新
在 layout-sidebar.ftl 的基础平台菜单中添加：
```html
<li>
    <a href="${ctx}/basicPlatform/fieldDependency/page"><i class="fa fa-circle-o"></i>字段依赖</a>
</li>
```

## 实现细节和注意事项

### 性能优化
1. **深度限制**: 最大层级3层，防止无限循环

### 错误处理
1. **循环依赖检测**: 在BFS过程中检测循环依赖，记录日志并跳过
2. **字段不存在**: 优雅处理字段或对象不存在的情况，记录日志并继续处理
3. **表达式解析错误**: 保证落库的数据是正确的，不要中断处理，记录日志即可
4. **缓存异常**: 首次实现时不需要考虑缓存, 直接查询数据库. 缓存查询失败时降级到直接数据库查询

### 扩展性设计
1. **插件化处理器**: 新增字段类型时只需添加新的处理器
2. **处理器路由**: 通过FieldProcessorRouter统一管理处理器
3. **Spring Cache集成**: 首次实现时不需要考虑缓存, 直接查询数据库
4. **包装类设计**: 通过FieldExt和ObjectExt提供类型安全的数据访问

### 性能优化
1. **批量查询**: 按对象批量查询字段信息，减少数据库连接次数

### 测试开发: 无, 我将自行测试

#### 编写单元测试: 不需要

package com.fxiaoke.paas.console.service.basicPlatform.fielddependency.processor

import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldExt
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.FieldProcessor
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.util.ExpressionParser
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * 引用字段处理器
 * 处理quote类型字段的依赖关系
 * <AUTHOR>
 * @date 2024/01/10
 */
@Component
@Slf4j
class QuoteFieldProcessor implements FieldProcessor {

    @Autowired
    private ExpressionParser expressionParser

    @Override
    List<FieldExt> processDependencies(FieldExt fieldExt, String tenantId) {
        List<FieldExt> dependencies = []
        
        try {
            // 解析逻辑：
            // 解析 quote_field, 值类似 "field_mk48c__c__r.field_xLe1P__c"
            // field_mk48c__c 表示本对象lookup字段/主从字段，返回其field_id
            // field_xLe1P__c 为lookup字段/主从字段目标对象的字段，返回其field_id
            String quoteField = fieldExt.getQuoteField()
            String currentObjectApiName = fieldExt.getDescribeApiName()
            
            if (StringUtils.isNotBlank(quoteField) && StringUtils.isNotBlank(currentObjectApiName)) {
                // 使用ExpressionParser解析quote_field
                List<FieldExt> quoteFields = expressionParser.parseQuoteField(quoteField, currentObjectApiName, tenantId)
                dependencies.addAll(quoteFields)
                
                log.debug("Quote字段 ${fieldExt.apiName} 依赖字段")
            }
        } catch (Exception e) {
            log.warn("处理Quote字段依赖失败: ${fieldExt.apiName}", e)
        }

        return dependencies
    }
}

package com.fxiaoke.paas.console.bean.basicPlatform.fielddependency

import lombok.Data
import lombok.EqualsAndHashCode

/**
 * 字段标识类
 * 通过对象API名称和字段API名称唯一标识一个字段
 * <AUTHOR>
 * @date 2024/01/10
 */
@Data
@EqualsAndHashCode
class FieldIdentifier {
    
    final String objectApiName
    final String fieldApiName
    @Delegate
    final FieldExt fieldExt

    FieldIdentifier(FieldExt fieldExt) {
        this.objectApiName = fieldExt.getDescribeApiName()
        this.fieldApiName = fieldExt.getApiName()
        this.fieldExt = fieldExt
    }
    
    FieldIdentifier(String objectApiName, String fieldApiName) {
        this.objectApiName = objectApiName
        this.fieldApiName = fieldApiName
        this.fieldExt = null
    }

    @Override
    boolean equals(Object anObject){
        return Objects.nonNull(anObject) \
                && anObject instanceof FieldIdentifier \
                && Objects.equals(objectApiName, anObject.objectApiName) \
                && Objects.equals(fieldApiName, anObject.fieldApiName) \
    }
    
    @Override
    String toString() {
        return "${objectApiName}.${fieldApiName}"
    }
}

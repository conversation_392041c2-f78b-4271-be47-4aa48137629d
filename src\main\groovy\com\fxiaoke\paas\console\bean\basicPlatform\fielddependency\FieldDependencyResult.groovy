package com.fxiaoke.paas.console.bean.basicPlatform.fielddependency

import groovy.transform.ToString
import lombok.Data

/**
 * 字段依赖分析结果
 * <AUTHOR>
 * @date 2024/01/10
 */
@Data
@ToString
class FieldDependencyResult {
    /**
     * 字段描述，key为field_id，value为字段完整信息
     */
    Map<FieldIdentifier, Map<String, Object>> fieldList
    
    /**
     * 使用邻接表表示的有向图，key为field_id，value为依赖的field_id列表
     */
    Map<FieldIdentifier, List<FieldIdentifier>> dependencyGraph
    
    /**
     * 涉及的对象API列表
     */
    List<String> objectApis
    
    /**
     * 根字段ID(field_id)
     */
    String rootFieldId
}

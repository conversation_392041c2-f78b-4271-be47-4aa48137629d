package com.fxiaoke.paas.console.service.basicPlatform.fielddependency.processor

import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldExt
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.FieldDescribeService
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.FieldProcessor
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.util.ExpressionParser
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * 统计字段处理器
 * 处理count类型字段的依赖关系
 * <AUTHOR>
 * @date 2024/01/10
 */
@Component
@Slf4j
class CountFieldProcessor implements FieldProcessor {

    @Autowired
    private ExpressionParser expressionParser
    
    @Autowired
    private FieldDescribeService fieldDescribeService

    @Override
    List<FieldExt> processDependencies(FieldExt fieldExt, String tenantId) {
        List<FieldExt> dependencies = []
        
        try {
            // 解析逻辑：
            // 1. sub_object_describe_api_name为被统计的目标对象
            // 2. wheres和lookup字段一致，解析过滤条件中的字段
            // 3. count_field_api_name 被统计对象上的被统计的目标字段
            String subObjectApiName = fieldExt.getSubObjectDescribeApiName()
            String wheres = fieldExt.getWheres()
            String countFieldApiName = fieldExt.getCountFieldApiName()
            
            if (StringUtils.isNotBlank(subObjectApiName)) {
                // 解析wheres条件中的字段
                if (StringUtils.isNotBlank(wheres)) {
                    List<FieldExt> whereFields = expressionParser.parseWheres(wheres, subObjectApiName, tenantId)
                    dependencies.addAll(whereFields)
                }
                
                // 解析被统计的目标字段
                if (StringUtils.isNotBlank(countFieldApiName)) {
                    FieldExt countField = fieldDescribeService.getFieldByObjectAndApi(tenantId, subObjectApiName, countFieldApiName)
                    if (countField) {
                        dependencies.add(countField)
                    }
                }
                
                log.debug("Count字段 ${fieldExt.apiName} 依赖子对象 ${subObjectApiName} 的字段")
            }
        } catch (Exception e) {
            log.warn("处理Count字段依赖失败: ${fieldExt.apiName}", e)
        }

        return dependencies
    }
}

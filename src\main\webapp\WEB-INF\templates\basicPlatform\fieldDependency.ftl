<#assign headContent>
    <link href="//static.foneshare.cn/oss/css/bootstrap-select.min.css" rel="stylesheet" type="text/css"/>
</#assign>
<style>
    .box {
        background-color: white;
        padding: 20px;
    }

    .form-inline .form-control {
        margin: 5px;
    }

    .content {
        width: 100%;
        padding: 20px;
    }

    #dependencyResult {
        margin-top: 20px;
    }

    #fieldTable {
        margin-top: 10px;
    }

    #graphContainer {
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 20px;
        text-align: center;
    }

    .dependency-info {
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 20px;
    }

    .dependency-info p {
        margin: 5px 0;
    }
</style>

<#assign bodyContent>
    <section class="content">
        <div class="box">
            <h3>字段依赖关系分析</h3>
            <div class="form-inline">
                <div class="form-group">
                    <label for="dependencyTenantId">企业ID:</label>
                    <input type="text" class="form-control" id="dependencyTenantId" name="tenantId" placeholder="请输入企业ei或ea">
                </div>
                <div class="form-group">
                    <label for="dependencyObjectApiName">对象ApiName:</label>
                    <input type="text" class="form-control" id="dependencyObjectApiName" name="objectApiName" placeholder="请输入对象ApiName">
                </div>
                <div class="form-group">
                    <label for="dependencyFieldApiName">字段ApiName:</label>
                    <input type="text" class="form-control" id="dependencyFieldApiName" name="fieldApiName" placeholder="请输入字段ApiName">
                </div>
                <button class="btn btn-primary" id="analyzeDependency">分析依赖关系</button>
                <button class="btn btn-info" id="visualizeDependency">可视化展示</button>
            </div>

            <div class="content">
                <div id="dependencyResult" style="display: none;">
                    <div class="dependency-info">
                        <h4>分析结果概览</h4>
                        <p><strong>根字段:</strong> <span id="rootFieldInfo"></span></p>
                        <p><strong>依赖字段数量:</strong> <span id="dependencyCount"></span></p>
                        <p><strong>涉及对象数量:</strong> <span id="objectCount"></span></p>
                        <p><strong>涉及对象:</strong> <span id="objectList"></span></p>
                    </div>

                    <div class="tabbable">
                        <ul class="nav nav-tabs">
                            <li class="active">
                                <a href="#dependencyList" data-toggle="tab">依赖字段列表</a>
                            </li>
                            <li>
                                <a href="#dependencyGraph" data-toggle="tab">依赖关系图</a>
                            </li>
                            <li>
                                <a href="#dependencyJson" data-toggle="tab">原始数据</a>
                            </li>
                        </ul>

                        <div class="tab-content">
                            <div class="tab-pane active" id="dependencyList">
                                <table class="table table-striped table-bordered" id="fieldTable">
                                    <thead>
                                        <tr>
                                            <th>字段名称</th>
                                            <th>字段标签</th>
                                            <th>字段类型</th>
                                            <th>所属对象</th>
                                            <th>依赖字段</th>
                                        </tr>
                                    </thead>
                                    <tbody id="fieldTableBody">
                                    </tbody>
                                </table>
                            </div>
                            <div class="tab-pane" id="dependencyGraph">
                                <div id="graphContainer" style="height: 500px;">
                                    <p style="margin-top: 200px;">点击"可视化展示"按钮生成依赖关系图</p>
                                </div>
                            </div>
                            <div class="tab-pane" id="dependencyJson">
                                <pre id="dependencyJsonContent" style="max-height: 400px; overflow-y: auto; background-color: #f5f5f5; padding: 15px; border-radius: 4px;"></pre>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="loadingInfo" style="display: none; text-align: center; padding: 50px;">
                    <p>数据加载中，请稍等...</p>
                </div>
            </div>
        </div>
    </section>
</#assign>

<#assign scriptContent>
    <script src="//static.foneshare.cn/oss/jquery/bootstrap-select.min.js"></script>
    <script type="application/javascript">
        $(document).ready(function () {
            // 字段依赖分析功能
            $('#analyzeDependency').on('click', function () {
                var tenantId = $('#dependencyTenantId').val()
                var objectApiName = $('#dependencyObjectApiName').val()
                var fieldApiName = $('#dependencyFieldApiName').val()
                
                if (!tenantId || !objectApiName || !fieldApiName) {
                    alert('请输入企业ID、对象ApiName和字段ApiName')
                    return
                }
                
                $('#loadingInfo').show()
                $('#dependencyResult').hide()
                
                $.ajax({
                    url: "/basicPlatform/fieldDependency/analyze",
                    type: "POST",
                    data: JSON.stringify({
                        "objectApiName": objectApiName,
                        "fieldApiName": fieldApiName
                    }),
                    contentType: "application/json",
                    dataType: "json",
                    success: function (result) {
                        $('#loadingInfo').hide()
                        if (result.code === 200) {
                            displayDependencyResult(result.data)
                        } else {
                            alert(result.message || '分析失败')
                        }
                    },
                    error: function (err) {
                        $('#loadingInfo').hide()
                        console.log(err.responseText)
                        alert('请求失败: ' + err.responseText)
                    }
                });
            })
            
            // 可视化展示功能
            $('#visualizeDependency').on('click', function () {
                var tenantId = $('#dependencyTenantId').val()
                var objectApiName = $('#dependencyObjectApiName').val()
                var fieldApiName = $('#dependencyFieldApiName').val()
                
                if (!tenantId || !objectApiName || !fieldApiName) {
                    alert('请输入企业ID、对象ApiName和字段ApiName')
                    return
                }
                
                $.ajax({
                    url: "/basicPlatform/fieldDependency/visualize",
                    type: "POST",
                    data: JSON.stringify({
                        "objectApiName": objectApiName,
                        "fieldApiName": fieldApiName
                    }),
                    contentType: "application/json",
                    dataType: "json",
                    success: function (result) {
                        if (result.code === 200) {
                            displayDependencyGraph(result.data)
                            // 切换到依赖关系图tab
                            $('a[href="#dependencyGraph"]').tab('show')
                        } else {
                            alert(result.message || '可视化失败')
                        }
                    },
                    error: function (err) {
                        console.log(err.responseText)
                        alert('请求失败: ' + err.responseText)
                    }
                });
            })
        });
        
        // 显示依赖分析结果
        function displayDependencyResult(data) {
            $('#dependencyResult').show()
            
            // 显示基本信息
            var rootField = data.fieldList[data.rootFieldId]
            $('#rootFieldInfo').text((rootField.field_label || rootField.api_name) + ' (' + rootField.api_name + ')')
            $('#dependencyCount').text(Object.keys(data.fieldList).length)
            $('#objectCount').text(data.objectApis.length)
            $('#objectList').text(data.objectApis.join(', '))
            
            // 构建字段列表表格
            var tableBody = $('#fieldTableBody')
            tableBody.empty()
            
            for (var fieldId in data.fieldList) {
                var field = data.fieldList[fieldId]
                var dependencies = data.dependencyGraph[fieldId] || []
                var dependencyNames = dependencies.map(function(depId) {
                    var depField = data.fieldList[depId]
                    return depField ? (depField.field_label || depField.api_name) : depId
                }).join(', ')
                
                var row = '<tr>' +
                    '<td>' + (field.api_name || '') + '</td>' +
                    '<td>' + (field.field_label || '') + '</td>' +
                    '<td>' + (field.type || '') + '</td>' +
                    '<td>' + (field.describe_api_name || '') + '</td>' +
                    '<td>' + dependencyNames + '</td>' +
                    '</tr>'
                tableBody.append(row)
            }
            
            // 显示原始JSON数据
            $('#dependencyJsonContent').text(JSON.stringify(data, null, 2))
        }
        
        // 显示依赖关系图
        function displayDependencyGraph(data) {
            var graphContainer = $('#graphContainer')
            var html = '<h5>依赖关系图</h5>'
            html += '<div class="dependency-info">'
            html += '<p><strong>根节点:</strong> ' + data.rootFieldId + '</p>'
            html += '<p><strong>节点数量:</strong> ' + data.nodes.length + '</p>'
            html += '<p><strong>边数量:</strong> ' + data.edges.length + '</p>'
            html += '</div>'
            html += '<div style="text-align: left; max-height: 300px; overflow-y: auto;">'
            
            data.edges.forEach(function(edge) {
                var fromNode = data.nodes.find(function(n) { return n.id === edge.from })
                var toNode = data.nodes.find(function(n) { return n.id === edge.to })
                if (fromNode && toNode) {
                    html += '<p><strong>' + fromNode.label + '</strong> → <strong>' + toNode.label + '</strong></p>'
                }
            })
            
            html += '</div>'
            html += '<p style="margin-top: 20px; color: #666;"><em>注：可以集成D3.js、vis.js等图形库来显示更丰富的可视化效果</em></p>'
            graphContainer.html(html)
        }
    </script>
</#assign>

<#include "../layout/layout-main.ftl" />

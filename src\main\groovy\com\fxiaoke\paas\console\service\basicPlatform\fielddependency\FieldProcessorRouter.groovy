package com.fxiaoke.paas.console.service.basicPlatform.fielddependency

import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldExt
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.processor.CountFieldProcessor
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.processor.DefaultValueFieldProcessor
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.processor.EmptyFieldProcessor
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.processor.FormulaFieldProcessor
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.processor.LookupFieldProcessor
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.processor.MasterDetailFieldProcessor
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.processor.QuoteFieldProcessor
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * 字段处理器路由类
 * 根据字段类型选择合适的处理器，实现策略模式
 * <AUTHOR>
 * @date 2024/01/10
 */
@Component
@Slf4j
class FieldProcessorRouter {

    @Autowired
    private CountFieldProcessor countFieldProcessor
    
    @Autowired
    private DefaultValueFieldProcessor defaultValueFieldProcessor
    
    @Autowired
    private FormulaFieldProcessor formulaFieldProcessor
    
    @Autowired
    private QuoteFieldProcessor quoteFieldProcessor
    
    @Autowired
    private LookupFieldProcessor lookupFieldProcessor
    
    @Autowired
    private MasterDetailFieldProcessor masterDetailFieldProcessor

    @Autowired
    private EmptyFieldProcessor emptyProcessor

    // 处理器缓存，key为字段类型，value为处理器
    private Map<String, FieldProcessor> processorCache = [
        "count": countFieldProcessor,
        "formula": formulaFieldProcessor,
        "quote": quoteFieldProcessor,
        "object_reference": lookupFieldProcessor,
        "object_reference_many": lookupFieldProcessor,
        "master_detail": masterDetailFieldProcessor
    ] as Map<String, FieldProcessor>

    /**
     * 根据字段信息获取对应的处理器
     * @param fieldExt 字段信息包装对象
     * @return 对应的处理器
     */
    FieldProcessor getProcessor(FieldExt fieldExt) {
        String fieldType = fieldExt.getType()

        // 根据字段类型获取处理器
        FieldProcessor processor = processorCache.get(fieldType)
        if (processor != null) {
            log.debug("选择处理器: ${processor.class.simpleName} for field type: ${fieldType}")
            return processor
        }

        // 其他类型的字段 default_is_expression 为 true 使用DefaultValueFieldProcessor
        Boolean defaultIsExpression = fieldExt.getDefaultIsExpression()
        if (defaultIsExpression != null && defaultIsExpression) {
            log.debug("使用DefaultValueFieldProcessor处理字段: ${fieldExt.apiName}")
            return defaultValueFieldProcessor
        }

        // 默认返回空处理器
        log.debug("使用默认空处理器 for field type: ${fieldType}")
        return emptyProcessor
    }
}

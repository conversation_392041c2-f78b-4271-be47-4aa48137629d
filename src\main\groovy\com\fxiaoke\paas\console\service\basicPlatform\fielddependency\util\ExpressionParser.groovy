package com.fxiaoke.paas.console.service.basicPlatform.fielddependency.util

import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldExt
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.FieldDescribeService
import groovy.json.JsonSlurper
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * 表达式解析工具类
 * 用于解析各种表达式如formula表达式、wheres条件、quote_field等的依赖字段
 * <AUTHOR>
 * @date 2024/01/10
 */
@Component
@Slf4j
class ExpressionParser {

    @Autowired
    private FieldDescribeService fieldDescribeService

    // 使用正则表达式提取$$包裹的变量: \$([^$]+)\$
    private static final Pattern pattern = Pattern.compile(/\$([^$]+)\$/)

    /**
     * 解析表达式中的字段引用
     * @param expression 表达式字符串，如 "$field_j1__c$ + $field_j2__c__r.field_3__c$"
     * @param currentObjectApiName 当前对象API名称
     * @param tenantId 企业ID
     * @return 依赖的字段描述列表
     */
    List<FieldExt> parseExpression(String expression, String currentObjectApiName, String tenantId) {
        if (StringUtils.isBlank(expression)) {
            return Collections.emptyList()
        }

        List<FieldExt> fields = []
        try {
            Matcher matcher = pattern.matcher(expression)

            while (matcher.find()) {
                String variable = matcher.group(1)
                fields.addAll(parseVariable(variable, currentObjectApiName, tenantId))
            }

            return fields
        } catch (Exception e) {
            log.warn("解析表达式失败: ${expression}", e)
            return Collections.emptyList()
        }
    }

    /**
     * 解析wheres条件中的字段引用
     * @param wheres JSON字符串
     * @param targetObjectApiName 目标对象API名称
     * @param tenantId 企业ID
     * @return 依赖的字段描述列表
     */
    List<FieldExt> parseWheres(String wheres, String targetObjectApiName, String tenantId) {
        if (StringUtils.isBlank(wheres)) {
            return Collections.emptyList()
        }

        try {
            List<FieldExt> fields = []
            def wheresList = new JsonSlurper().parseText(wheres)

            wheresList.each { whereClause ->
                def filters = whereClause.filters
                filters?.each { filter ->
                    String fieldName = filter.field_name
                    if (StringUtils.isNotBlank(fieldName)) {
                        FieldExt field = fieldDescribeService.getFieldByObjectAndApi(tenantId, targetObjectApiName, fieldName)
                        if (field) {
                            fields.add(field)
                        }
                    }
                }
            }

            return fields.unique()
        } catch (Exception e) {
            log.warn("解析wheres条件失败: ${wheres}", e)
            return Collections.emptyList()
        }
    }

    /**
     * 解析quote_field中的字段引用
     * @param quoteField 如 "field_mk48c__c__r.field_xLe1P__c"
     * @param currentObjectApiName 当前对象API名称
     * @param tenantId 企业ID
     * @return 依赖的字段描述列表
     */
    List<FieldExt> parseQuoteField(String quoteField, String currentObjectApiName, String tenantId) {
        if (StringUtils.isBlank(quoteField)) {
            return Collections.emptyList()
        }

        return parseVariable(quoteField, currentObjectApiName, tenantId)
    }

    /**
     * 解析变量引用
     * @param variable 变量名，如 "field_j1__c" 或 "field_j2__c__r.field_3__c"
     * @param currentObjectApiName 当前对象API名称
     * @param tenantId 企业ID
     * @return 依赖的字段描述列表
     */
    private List<FieldExt> parseVariable(String variable, String currentObjectApiName, String tenantId) {
        List<FieldExt> fields = []

        try {
            if (variable.contains('.')) {
                // 关联字段引用: xxx__c__r.yyy__c
                String[] parts = variable.split('\\.')
                if (parts.length == 2) {
                    String lookupFieldName = parts[0].replace('__r', '')  // 去掉__r后缀
                    String targetFieldName = parts[1]

                    // 1. 查询本对象的lookup字段
                    FieldExt lookupField = fieldDescribeService.getFieldByObjectAndApi(tenantId, currentObjectApiName, lookupFieldName)
                    if (lookupField) {
                        fields.add(lookupField)

                        // 2. 查询目标对象的字段
                        // 需要先获取lookup字段的target_api_name
                        String targetObjectApiName = lookupField.getTargetApiName()
                        if (targetObjectApiName) {
                            FieldExt targetField = fieldDescribeService.getFieldByObjectAndApi(tenantId, targetObjectApiName, targetFieldName)
                            if (targetField) {
                                fields.add(targetField)
                            }
                        }
                    }
                }
            } else {
                // 本对象字段引用: xxx__c
                FieldExt field = fieldDescribeService.getFieldByObjectAndApi(tenantId, currentObjectApiName, variable)
                if (field) {
                    fields.add(field)
                }
            }
        } catch (Exception e) {
            log.warn("解析变量失败: ${variable}", e)
        }

        return fields
    }
}

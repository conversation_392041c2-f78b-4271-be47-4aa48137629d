# PAAS-Console 新功能开发指南

## 开发前准备

### 环境要求
- JDK 8+
- Maven 3.x
- IDE支持Groovy (推荐IntelliJ IDEA)
- 了解Spring MVC + MyBatis架构

### 项目启动
```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包部署
mvn clean package
```

## 新功能开发步骤

### 第一步：需求分析和设计

#### 1. 确定功能边界
- 明确新功能的具体需求和业务场景
- 确定是否需要多租户支持
- 确定数据源类型(PostgreSQL/MySQL/ClickHouse/MongoDB)

#### 2. 设计数据模型
- 如果需要新表，设计数据库表结构
- 确定是否需要跨租户数据访问
- 考虑数据权限和安全性要求

### 第二步：创建基础结构

#### 1. 创建包结构 (按现有模式)
```
src/main/groovy/com/fxiaoke/paas/console/
├── bean/yourmodule/          # DTO对象
├── entity/yourmodule/        # 数据库实体
├── mapper/yourmodule/        # MyBatis Mapper
├── service/yourmodule/       # 业务逻辑服务
└── web/yourmodule/           # Controller控制器
```

#### 2. 创建实体类 (`entity/yourmodule/`)
```groovy
@Data
class YourEntity {
    private String id
    private String tenantId  // 多租户必须
    private String name
    private Long createTime
    private String createBy
}
```

### 第三步：数据访问层开发

#### 1. 创建Mapper接口 (`mapper/yourmodule/YourMapper.groovy`)
```groovy
@Mapper
interface YourMapper {
    List<YourEntity> findByTenantId(@Param("tenantId") String tenantId)
    int insert(YourEntity entity)
    int update(YourEntity entity)
}
```

#### 2. 创建MyBatis XML映射文件
```xml
<!-- src/main/resources/mapper/yourmodule/YourMapper.xml -->
<mapper namespace="com.fxiaoke.paas.console.mapper.yourmodule.YourMapper">
    <select id="findByTenantId" resultType="YourEntity">
        SELECT * FROM your_table WHERE tenant_id = #{tenantId}
    </select>
</mapper>
```

### 第四步：业务逻辑层开发

#### 1. 创建Service接口和实现 (`service/yourmodule/`)
```groovy
@Service
class YourService {
    @Autowired
    private YourMapper yourMapper
    
    List<YourEntity> queryByTenantId(String tenantId) {
        return yourMapper.findByTenantId(tenantId)
    }
}
```

#### 2. 集成多租户支持 (如果需要)
```groovy
// 使用ConsoleTenantPolicy获取租户上下文
TenantContext context = consoleTenantPolicy.get(tenantId, false)
```

### 第五步：控制器层开发

#### 1. 创建Controller (`web/yourmodule/YourController.groovy`)
```groovy
@Controller
@RequestMapping("/yourmodule")
@Slf4j
class YourController {
    @Autowired
    private YourService yourService
    
    @RequestMapping("/page")
    def page() {
        return "yourmodule/index"  // 对应FreeMarker模板
    }
    
    @RequestMapping("/query")
    @ResponseBody
    @SystemControllerLog(description = "您的模块 -- 查询数据")
    def query(@RequestParam String tenantId) {
        try {
            List<YourEntity> result = yourService.queryByTenantId(tenantId)
            return ["code": 200, "data": result]
        } catch (Exception e) {
            log.error("查询失败", e)
            return ["code": 500, "message": e.getMessage()]
        }
    }
}
```

### 第六步：前端页面开发

#### 1. 创建FreeMarker模板 (`src/main/webapp/WEB-INF/templates/yourmodule/index.ftl`)
```html
<#assign bodyContent>
<section class="content">
    <div class="box">
        <div class="box-header">
            <h3 class="box-title">您的功能模块</h3>
        </div>
        <div class="box-body">
            <!-- 功能界面内容 -->
        </div>
    </div>
</section>
</#assign>
<#include "../layout/layout-main.ftl" />
```

#### 2. 添加导航菜单 (修改 `layout-sidebar.ftl`)
```html
<li class="treeview">
    <a href="${ctx}/yourmodule/page">
        <i class="fa fa-your-icon"></i>
        <span>您的功能</span>
    </a>
</li>
```

### 第七步：测试开发: 若非明确指出, 不需要执行

#### 1. 创建HTTP测试文件 (`http/YourModule.http`)
```http
### 查询数据
GET http://localhost:8080/yourmodule/query?tenantId=test123

### 创建数据
POST http://localhost:8080/yourmodule/create
Content-Type: application/json

{
  "tenantId": "test123",
  "name": "测试数据"
}
```

#### 2. 编写单元测试 (使用Spock框架): 若非明确指出, 不需要执行
```groovy
class YourServiceSpec extends Specification {
    def "should query data by tenant id"() {
        given:
        def service = new YourService()
        
        when:
        def result = service.queryByTenantId("test123")
        
        then:
        result != null
    }
}
```

## 开发注意事项

### 必须遵循的规范
1. **多租户支持**: 所有数据操作都必须包含tenantId参数
2. **权限控制**: 敏感操作需要添加`@SystemControllerLog`注解
3. **异常处理**: 统一的异常处理和错误返回格式
4. **配置管理**: 使用`ConfigFactory.getConfig()`获取配置
5. **日志记录**: 使用`@Slf4j`注解进行日志记录
6. **数据脱敏**: 敏感数据需要使用`DataMaskingService`
7. **缓存使用**: 频繁查询的数据考虑使用Redis缓存

### 技术集成要点
- **数据库路由**: 使用`DbRouterClient`进行多租户数据源路由
- **外部服务调用**: 使用`OKHttpService`调用外部HTTP API
- **Dubbo服务**: 通过`OrgDubboService`调用组织架构服务
- **MongoDB操作**: 工作流相关数据使用MongoDB存储
- **Redis缓存**: 数据权限和会话信息使用Redis缓存

### 错误处理模式
```groovy
try {
    // 业务逻辑
    return ["code": 200, "data": result]
} catch (Exception e) {
    log.error("操作失败", e)
    return ["code": 500, "message": e.getMessage()]
}
```

### 审计日志记录
```groovy
@SystemControllerLog(description = "模块名 -- 具体操作描述")
```

## 常用工具和服务

### 配置获取
```groovy
ConfigFactory.getConfig("config-name", { iConfig ->
    String value = iConfig.get("key")
})
```

### HTTP调用
```groovy
@Autowired
private OKHttpService okHttpService

String result = okHttpService.getForm(url)
String result = okHttpService.postJSON(url, jsonObject)
```

### 数据脱敏
```groovy
@Autowired
private DataMaskingService dataMaskingService

String maskedData = dataMaskingService.mask(originalData)
```

## 部署和测试

### API测试
- 使用项目提供的HTTP测试文件进行接口测试
- 在IntelliJ IDEA中直接执行.http文件

### 部署方式
- **打包格式**: WAR包，支持Tomcat/Jetty容器
- **配置管理**: 基于配置中心的动态配置
- **监控集成**: JVM监控、应用性能监控
- **日志系统**: Logback日志框架，支持日志轮转

## 现有可复用的Service和Mapper

### 核心通用Service (可直接使用)

#### 1. HTTP调用服务 (`OKHttpService`)
```groovy
@Autowired
private OKHttpService okHttpService

// GET请求
String result = okHttpService.getForm(url)

// POST JSON请求
String result = okHttpService.postJSON(url, jsonObject)
String result = okHttpService.postJSON(url, jsonArray)

// POST表单请求
String result = okHttpService.postForm(url, paramMap)

// 带Headers的请求
String result = okHttpService.postJSONWithHeaders(url, jsonObject, headers)
```

#### 2. 数据脱敏服务 (`DataMaskingService`)
```groovy
@Autowired
private DataMaskingService dataMaskingService

// 根据字段类型自动脱敏
Object maskedData = DataMaskingService.filter(data, fieldType)
// 支持的类型: name, phone_number, email, currency, location, image, file_attachment
```

#### 3. 多租户策略服务 (`ConsoleTenantPolicy`)
```groovy
@Autowired
private ConsoleTenantPolicy consoleTenantPolicy

// 获取租户上下文
TenantContext context = consoleTenantPolicy.get(tenantId, false)
```

#### 4. 数据库连接服务 (`JdbcService`)
```groovy
@Autowired
private JdbcService jdbcService

// 获取数据库连接并执行查询
JdbcConnection connection = jdbcService.connection(jdbcUrl)
connection.query(sql, { rs ->
    while (rs.next()) {
        // 处理结果集
    }
})
```

#### 5. 组织架构Dubbo服务 (`OrgDubboService`)
```groovy
@Autowired
private OrgDubboService orgDubboService

// 调用组织架构相关的Dubbo服务
// 获取企业信息、部门信息、员工信息等
```

### 元数据相关Service

#### 1. 数据查询服务 (`DataQueryService`)
- `findApiNameList(tenantId, describeApiName)`: 查询对象的字段API名称列表
- `findDataByScene(SearchSceneArg)`: 按场景查询数据，支持db/es数据源

#### 2. 元数据描述服务 (`DescribeService`)
- `describeList(tenantId, apiName)`: 查询对象描述列表
- `apiNameAndDisplayName(tenantId, describeId)`: 获取API名称和显示名称映射
- `getAllStoreTableNames()`: 获取所有存储表名映射

#### 3. 数据服务 (`DataService`)
- `dataList(tenantId, describeId)`: 查询数据列表
- `dataInfo(tenantId, apiName, id)`: 查询数据详情
- `getDataByName(tenantId, apiName, name)`: 按名称查询数据

#### 4. 字段服务 (`FieldService`)
- 字段信息查询和管理
- 字段权限验证

#### 5. 统计服务 (`StatService`)
- 数据统计和分析功能

### 数据权限相关Service

#### 1. 数据权限查询服务 (`DataRightsQueryService`)
- `getDataAuthMsg(tenantId, objectId, tableName)`: 获取数据权限信息
- `checkDataAuth(tenantId, objectId, tableName, apiName)`: 检查数据权限

#### 2. 数据权限数据服务 (`DataRightsDataService`)
- 数据权限的计算和管理
- 权限缓存的初始化和刷新

#### 3. 数据权限存储表名服务 (`DataRightsStoreTableNameService`)
- `byDescribeApiName(tenantId, apiName)`: 根据API名称获取存储表名

### DTS相关Service

#### 1. DTS服务 (`DtsService`)
- `checkDtsData(DtsCheckDataArg)`: 数据一致性验证
- `generateDtsConfig(DtsConfigArg)`: 生成DTS配置
- `checkJdbcUrl(DtsConfigArg)`: 检查JDBC连接
- `getDtsTopicByEi(ei)`: 获取DTS Topic信息

### 基础平台Service

#### 1. 计算服务 (`CalculateService`)
- `triggerCalculateAndUpdate(tenantId, dataIds, fieldApiNames, objectApiName)`: 触发字段计算

#### 2. 调度任务服务 (`SchedulerTaskService`)
- `batchCancel(method, arg)`: 批量取消调度任务

#### 3. 重复数据刷新服务 (`DuplicatedRefreshService`)
- `refreshTenant(refreshType, tenantIdList, failTenant)`: 批量刷新企业数据

### 功能权限相关Service

#### 1. 功能服务 (`FuncService`)
- `queryRoleCodesByTenantId(tenantId)`: 查询租户角色代码
- `queryUsersByTenantIdAndRoleCode(tenantId, roleCode)`: 查询角色下的用户

### 许可证相关Service

#### 1. 许可证服务 (`LicenseService`)
- 许可证查询和验证功能

### 常用Mapper接口

#### 元数据Mapper
- **DescribeMapper**: 对象描述查询
  - `describeListByTenantId(tenantId)`: 查询租户下的对象列表
  - `findStoreTableNameAndApiName(tenantId, describeId)`: 查询存储表名和API名称
- **FieldMapper**: 字段信息查询
  - `findFieldByDescribeId(describeId)`: 根据对象ID查询字段
  - `findFieldByTenantIdAndDescribe(tenantId, describeApiName)`: 查询对象字段
- **DataMapper**: 通用数据查询
- **StatMapper**: 统计查询

#### 数据权限Mapper
- **DataRightsQueryMapper**: 数据权限查询
- **PersonnelMapper**: 人员信息查询
- **DataRightsDataMapper**: 数据权限数据操作

#### 功能权限Mapper
- **FuncMapper**: 功能权限查询
  - `queryRoleCodesByTenantId(schemaName, tenantId)`: 查询角色代码
  - `queryUsersByTenantIdAndRoleCode(schemaName, tenantId, roleCode)`: 查询用户角色

#### 日志Mapper
- **AuditLogMapper**: 审计日志操作
- **SpecialTableMapper**: 专表管理

#### 组织架构Mapper
- **DepartmentMapper**: 部门信息查询
- **PaasConsoleEmployeeMapper**: 员工信息查询

### 常用工具类

#### 1. 数据脱敏工具 (`DataMaskingUtil`)
```groovy
// 中文姓名脱敏
String maskedName = DataMaskingUtil.chineseName(fullName)

// 手机号脱敏
String maskedPhone = DataMaskingUtil.mobilePhone(phoneNumber)

// 身份证号脱敏
String maskedId = DataMaskingUtil.idCardNum(idCard)

// 邮箱脱敏
String maskedEmail = DataMaskingUtil.email(email)
```

#### 2. API名称转换工具 (`ApiNameToStoreTableNameUtil`)
```groovy
// API名称转存储表名
String tableName = ApiNameToStoreTableNameUtil.toNewStoreTableName(storeTableName)
```

#### 3. DTS工具 (`DtsInternUtils`)
```groovy
// 驼峰转下划线
String tableName = DtsInternUtils.toTableName(apiName)

// 字符串转小写
String lowerStr = DtsInternUtils.lower(str)
```

## 参考现有实现

建议参考以下现有模块的实现：
- **元数据管理**: `DataQueryController`、`DataQueryService`、`DescribeService`
- **数据权限**: `DataRightsController`、`DataRightsQueryService`
- **DTS传输**: `DtsController`、`DtsService`
- **工作流**: 工作流相关的Controller和Service
- **HAMSTER迁移**: `HamsterTaskController`

通过学习这些现有实现，可以更好地理解项目的架构模式和开发规范。

package com.fxiaoke.paas.console.bean.basicPlatform.fielddependency

import lombok.Data

/**
 * 字段信息包装类
 * <AUTHOR>
 * @date 2024/01/10
 */
@Data
class FieldExt {
    private Map<String, Object> fieldInfo

    FieldExt(Map<String, Object> fieldInfo) {
        this.fieldInfo = fieldInfo ?: [:]
    }

    // 基础字段信息
    String getFieldId() { 
        return fieldInfo.get("field_id") as String 
    }
    
    String getApiName() { 
        return fieldInfo.get("api_name") as String 
    }
    
    String getFieldLabel() { 
        return fieldInfo.get("field_label") as String ?: "" 
    }
    
    String getDescribeApiName() { 
        return fieldInfo.get("describe_api_name") as String 
    }
    
    String getType() { 
        return fieldInfo.get("type") as String ?: "text" 
    }

    // 关联字段信息
    String getTargetApiName() { 
        return fieldInfo.get("target_api_name") as String 
    }
    
    String getWheres() { 
        return fieldInfo.get("wheres") as String 
    }

    // 计算字段信息
    String getExpression() { 
        return fieldInfo.get("expression") as String 
    }

    // 统计字段信息
    String getSubObjectDescribeApiName() { 
        return fieldInfo.get("sub_object_describe_api_name") as String 
    }
    
    String getCountFieldApiName() { 
        return fieldInfo.get("count_field_api_name") as String 
    }

    // 引用字段信息
    String getQuoteField() { 
        return fieldInfo.get("quote_field") as String 
    }

    // 默认值信息
    String getDefaultValue() { 
        return fieldInfo.get("default_value") as String 
    }
    
    Boolean getDefaultIsExpression() {
        def value = fieldInfo.get("default_is_expression")
        return value != null ? (value as Boolean) : false
    }

    // 获取原始字段信息
    Map<String, Object> getRawFieldInfo() { 
        return fieldInfo 
    }
}

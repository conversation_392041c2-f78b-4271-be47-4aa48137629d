package com.fxiaoke.paas.console.bean.basicPlatform.fielddependency

import lombok.Data

/**
 * 对象信息包装类
 * <AUTHOR>
 * @date 2024/01/10
 */
@Data
class ObjectExt {
    private Map<String, Object> objectInfo

    ObjectExt(Map<String, Object> objectInfo) {
        this.objectInfo = objectInfo ?: [:]
    }

    String getDescribeId() { 
        return objectInfo.get("describe_id") as String
    }
    
    String getDescribeApiName() { 
        return objectInfo.get("describe_api_name") as String
    }
    
    String getDisplayName() { 
        return objectInfo.get("display_name") as String
    }
    
    String getStoreTableName() { 
        return objectInfo.get("store_table_name") as String
    }

    // 获取原始对象信息
    Map<String, Object> getRawObjectInfo() { 
        return objectInfo 
    }
}

package com.fxiaoke.paas.console.service.basicPlatform.fielddependency

import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldDependencyResult
import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldExt
import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldIdentifier
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

/**
 * 字段依赖主服务类
 * 实现字段依赖关系分析的核心逻辑，使用BFS算法遍历字段依赖关系
 * <AUTHOR>
 * @date 2024/01/10
 */
@Service
@Slf4j
class FieldDependencyService {

    @Value('${field.dependency.max.level:10}')
    private Integer maxLevel

    @Autowired
    private FieldDescribeService fieldDescribeService
    
    @Autowired
    private FieldProcessorRouter processorRouter


    /**
     * 分析字段依赖关系
     * @param tenantId 企业ID
     * @param objectApiName 对象API名称
     * @param fieldApiName 字段API名称
     * @return 字段依赖分析结果
     */
    FieldDependencyResult analyzeDependency(String tenantId, String objectApiName, String fieldApiName) {
        log.info("开始分析字段依赖关系: ${tenantId}.${objectApiName}.${fieldApiName}")
        
        try {
            // 1. 预加载根对象的所有字段（请求级缓存）
            fieldDescribeService.loadObject(tenantId, objectApiName)

            // 2. 查询根字段信息
            FieldExt rootField = fieldDescribeService.getFieldByObjectAndApi(tenantId, objectApiName, fieldApiName)
            if (rootField == null) {
                throw new IllegalArgumentException("字段不存在: ${objectApiName}.${fieldApiName}")
            }

            // 3. 初始化结果对象
            FieldDependencyResult result = new FieldDependencyResult()
            result.rootFieldId = rootField.getFieldId()
            result.fieldList = [:]
            result.dependencyGraph = [:]
            result.objectApis = []

            // 4. BFS遍历依赖关系(按字段维度)
            traverseDependencies(tenantId, rootField, result)

            // 5. 收集涉及的对象API
            Set<String> objectApiSet = result.fieldList.keySet().collect {
                it.objectApiName
            }.toSet()
            result.objectApis = objectApiSet.toList()

            log.info("字段依赖分析完成，共发现 ${result.fieldList.size()} 个依赖字段，涉及 ${result.objectApis.size()} 个对象")
            return result
            
        } catch (Exception e) {
            log.error("字段依赖分析失败: ${tenantId}.${objectApiName}.${fieldApiName}", e)
            throw e
        }
    }

    /**
     * BFS遍历依赖关系(按字段维度)
     * @param tenantId 企业ID
     * @param rootField 根字段
     * @param result 结果对象
     */
    private void traverseDependencies(String tenantId, FieldExt rootField, FieldDependencyResult result) {
        Queue<FieldIdentifier> fieldQueue = new LinkedList<>()
        Set<FieldIdentifier> visitedFields = new HashSet<>()
        int deep = 0;

        // 初始化根字段
        def root = new FieldIdentifier(rootField)
        fieldQueue.offer(root)
        visitedFields.add(root)

        // 添加根字段到结果
        result.fieldList.put(root, rootField.getRawFieldInfo())
        result.dependencyGraph.put(root, [])

        while (!fieldQueue.isEmpty()) {
            deep++
            // 检查层级限制
            if (deep >= maxLevel) {
                log.debug("达到最大层级限制 ${maxLevel}，跳过字段: ${currentFieldId}")
                continue
            }

            // 获取当前字段信息
            FieldIdentifier currentField = fieldQueue.poll()

            // 分析当前字段的依赖
            List<FieldIdentifier> dependencies = processorRouter.getProcessor(currentField.getFieldExt())
                .processDependencies(currentField.getFieldExt(), tenantId)
                .stream().map { new FieldIdentifier(it) }
                .collect()

            // 检测循环依赖
            if (dependencies.contains(currentField)) {
                log.warn("检测到循环依赖: ${currentField}")
                dependencies.remove(currentField)
            }

            // 更新依赖图
            result.dependencyGraph.put(currentField, dependencies)

            // 处理依赖字段
            for (FieldIdentifier depField : dependencies) {
                if (!visitedFields.contains(depField) && depField != null) {
                    fieldQueue.offer(depField)
                    visitedFields.add(depField)

                    // 添加到结果
                    result.fieldList.put(depField, depField.getRawFieldInfo())

                    log.debug("发现依赖字段: ${currentField} -> ${depField}")
                }
            }

        }
    }
}
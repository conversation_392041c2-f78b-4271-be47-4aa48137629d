package com.fxiaoke.paas.console.service.basicPlatform.fielddependency.processor

import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldExt
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.FieldProcessor
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.util.ExpressionParser
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * Lookup字段处理器
 * 处理object_reference和object_reference_many类型字段的依赖关系
 * <AUTHOR>
 * @date 2024/01/10
 */
@Component
@Slf4j
class LookupFieldProcessor implements FieldProcessor {

    @Autowired
    private ExpressionParser expressionParser

    @Override
    List<FieldExt> processDependencies(FieldExt fieldExt, String tenantId) {
        List<FieldExt> dependencies = []
        
        try {
            // 解析逻辑：
            // 1. target_api_name 对应的目标对象api
            // 2. 解析 wheres 字段，获取涉及的目标对象字段
            String targetApiName = fieldExt.getTargetApiName()
            String wheres = fieldExt.getWheres()
            
            if (StringUtils.isNotBlank(targetApiName) && StringUtils.isNotBlank(wheres)) {
                // 使用ExpressionParser解析wheres条件
                List<FieldExt> whereFields = expressionParser.parseWheres(wheres, targetApiName, tenantId)
                dependencies.addAll(whereFields)
                
                log.debug("Lookup字段 ${fieldExt.apiName} 依赖目标对象 ${targetApiName} 的字段")
            }
        } catch (Exception e) {
            log.warn("处理Lookup字段依赖失败: ${fieldExt.apiName}", e)
        }

        return dependencies
    }
}

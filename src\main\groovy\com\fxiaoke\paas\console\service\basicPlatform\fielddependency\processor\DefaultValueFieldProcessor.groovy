package com.fxiaoke.paas.console.service.basicPlatform.fielddependency.processor

import com.fxiaoke.paas.console.bean.basicPlatform.fielddependency.FieldExt
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.FieldProcessor
import com.fxiaoke.paas.console.service.basicPlatform.fielddependency.util.ExpressionParser
import groovy.util.logging.Slf4j
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * 默认值字段处理器
 * 处理default_is_expression为true的字段的依赖关系
 * <AUTHOR>
 * @date 2024/01/10
 */
@Component
@Slf4j
class DefaultValueFieldProcessor implements FieldProcessor {

    @Autowired
    private ExpressionParser expressionParser

    @Override
    List<FieldExt> processDependencies(FieldExt fieldExt, String tenantId) {
        List<FieldExt> dependencies = []
        
        try {
            // 解析逻辑：
            // 需要解析default_value字段，规则和expression一致
            String defaultValue = fieldExt.getDefaultValue()
            String currentObjectApiName = fieldExt.getDescribeApiName()
            
            if (StringUtils.isNotBlank(defaultValue) && StringUtils.isNotBlank(currentObjectApiName)) {
                // 使用ExpressionParser解析default_value表达式
                List<FieldExt> defaultValueFields = expressionParser.parseExpression(defaultValue, currentObjectApiName, tenantId)
                dependencies.addAll(defaultValueFields)
                
                log.debug("DefaultValue字段 ${fieldExt.apiName} 依赖字段")
            }
        } catch (Exception e) {
            log.warn("处理DefaultValue字段依赖失败: ${fieldExt.apiName}", e)
        }

        return dependencies
    }
}
